<?php
// PHPMailer ile SMTP kullanımı (canlı sunucuda bu dosyayı kullanın)

// PHPMailer kütüpha<PERSON><PERSON> yükle
use PHPMailer\PHPMailer\PHPMailer;
use PHPMailer\PHPMailer\SMTP;
use PHPMailer\PHPMailer\Exception;

require_once 'vendor/autoload.php'; // Composer ile yüklediyseniz
// VEYA manuel include:
// require_once 'phpmailer/src/Exception.php';
// require_once 'phpmailer/src/PHPMailer.php';
// require_once 'phpmailer/src/SMTP.php';

// Form verilerini kontrol et
if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    header('Location: ?page=contact');
    exit;
}

// Form verilerini al ve temizle
$name = trim($_POST['name'] ?? '');
$email = trim($_POST['email'] ?? '');
$phone = trim($_POST['phone'] ?? '');
$company = trim($_POST['company'] ?? '');
$subject = trim($_POST['subject'] ?? '');
$message = trim($_POST['message'] ?? '');

// Zorunlu alanları kontrol et
if (empty($name) || empty($email) || empty($subject) || empty($message)) {
    header('Location: ?page=contact&error=required_fields');
    exit;
}

// E-posta adresini doğrula
if (!filter_var($email, FILTER_VALIDATE_EMAIL)) {
    header('Location: ?page=contact&error=invalid_email');
    exit;
}

// Konu başlıklarını Türkçe'ye çevir
$subject_titles = [
    'urun_bilgisi' => 'Ürün Bilgisi',
    'fiyat_teklifi' => 'Fiyat Teklifi',
    'ihracat' => 'İhracat İşbirliği',
    'lojistik' => 'Lojistik Hizmetleri',
    'genel' => 'Genel Bilgi'
];

$subject_title = $subject_titles[$subject] ?? 'Genel Bilgi';

try {
    // PHPMailer nesnesi oluştur
    $mail = new PHPMailer(true);

    // SMTP ayarları - BURASI ÖNEMLİ!
    $mail->isSMTP();
    $mail->Host       = 'smtp.mekameyvecilik.com';  // SMTP sunucu adresi
    $mail->SMTPAuth   = true;
    $mail->Username   = '<EMAIL>'; // SMTP kullanıcı adı
    $mail->Password   = 'SMTP_SIFRENIZ';              // SMTP şifre
    $mail->SMTPSecure = PHPMailer::ENCRYPTION_STARTTLS;
    $mail->Port       = 587;                          // SMTP port (587 veya 465)
    $mail->CharSet    = 'UTF-8';

    // Mail içeriği
    $mail_body = "
MEKA MEYVECİLİK WEB SİTESİ İLETİŞİM FORMU
==========================================

Gönderen Bilgileri:
- Ad Soyad: $name
- E-posta: $email
- Telefon: " . ($phone ?: 'Belirtilmemiş') . "
- Firma: " . ($company ?: 'Belirtilmemiş') . "

Konu: $subject_title

Mesaj:
$message

==========================================
Gönderim Tarihi: " . date('d.m.Y H:i:s') . "
IP Adresi: " . ($_SERVER['REMOTE_ADDR'] ?? 'Bilinmiyor') . "
";

    // Size gönderilecek mail
    $mail->setFrom('<EMAIL>', 'Meka Meyvecilik Website');
    $mail->addAddress('<EMAIL>', 'Meka Meyvecilik');
    $mail->addReplyTo($email, $name);

    $mail->isHTML(false);
    $mail->Subject = "Meka Meyvecilik - $subject_title Talebi - $name";
    $mail->Body    = $mail_body;

    // İlk maili gönder
    $mail->send();

    // Otomatik yanıt maili hazırla
    $mail->clearAddresses();
    $mail->addAddress($email, $name);

    $auto_reply_body = "
Sayın $name,

Meka Meyvecilik ile iletişime geçtiğiniz için teşekkür ederiz.

Mesajınız başarıyla alınmıştır ve en kısa sürede size geri dönüş yapacağız.

Mesaj Özeti:
- Konu: $subject_title
- Tarih: " . date('d.m.Y H:i:s') . "

İyi günler dileriz.

Meka Meyvecilik
<EMAIL>
+90 546 802 19 91
";

    $mail->Subject = "Meka Meyvecilik - Mesajınız Alındı";
    $mail->Body    = $auto_reply_body;

    // Otomatik yanıt gönder
    $mail->send();

    // Başarılı
    header('Location: ?page=contact&success=1');

} catch (Exception $e) {
    // Hata durumunda
    error_log("Mail Error: " . $mail->ErrorInfo);
    header('Location: ?page=contact&error=send_failed');
}

exit;
?>