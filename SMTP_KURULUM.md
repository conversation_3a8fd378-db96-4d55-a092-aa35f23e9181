# SMTP Kurulum Rehberi - Meka Meyvecilik

## Canlı Sunucuda Mail Ayarları

### Seçenek 1: Mevcut Kod (Basit mail() fonksiyonu)
Mevcut `send_message.php` dosyası sunucunuzun mail ayarlarını kullanır.

**Hosting sağlayıcınızdan şunları öğrenin:**
- SMTP sunucu adresi (örn: `mail.mekameyvecilik.com`)
- SMTP port (genellikle 587 veya 465)
- Mail hesabı kullanıcı adı ve şifre

**cPanel'de Mail ayarları:**
1. cPanel → Mail → Email Accounts
2. Yeni mail hesabı oluşturun: `<EMAIL>`
3. PHP Mail ayarlarını kontrol edin

### Seçenek 2: PHPMailer ile SMTP (ÖNERİLEN)

#### 1. PHPMailer Kurulumu

**Composer ile:**
```bash
composer require phpmailer/phpmailer
```

**<PERSON> k<PERSON>:**
1. https://github.com/PHPMailer/PHPMailer/releases adresinden indirin
2. `phpmailer` klasörünü sitenizin kök dizinine yükleyin

#### 2. Dosya Değişiklikleri

**contact.php dosyasında form action'ını değiştirin:**
```php
<form action="send_message_smtp.php" method="POST">
```

#### 3. SMTP Ayarları (send_message_smtp.php dosyasında)

**Gmail kullanıyorsanız:**
```php
$mail->Host       = 'smtp.gmail.com';
$mail->Username   = '<EMAIL>';
$mail->Password   = 'uygulama_sifresi';  // Gmail App Password
$mail->Port       = 587;
```

**Kendi domain mailinizi kullanıyorsanız:**
```php
$mail->Host       = 'mail.mekameyvecilik.com';  // Hosting sağlayıcınızdan öğrenin
$mail->Username   = '<EMAIL>';
$mail->Password   = 'mail_hesabi_sifresi';
$mail->Port       = 587;  // veya 465
```

**Popüler hosting sağlayıcıları:**

| Hosting | SMTP Server | Port |
|---------|-------------|------|
| cPanel (Genel) | mail.yourdomain.com | 587/465 |
| Turhost | mail.turhost.com | 587 |
| Natro | mail.natro.com | 587 |
| Hosting.com.tr | mail.hosting.com.tr | 587 |

#### 4. Güvenlik Ayarları

**Gmail için App Password oluşturma:**
1. Google Account → Security → 2-Step Verification
2. App Passwords → Select app: Mail
3. Oluşturulan şifreyi kullanın

**Domain mail için:**
1. cPanel → Email Accounts
2. Email hesabı oluşturun
3. Güçlü şifre kullanın

#### 5. Test Etme

**Canlı sunucuda test:**
```bash
# SSH ile sunucuya bağlanın
curl -X POST https://mekameyvecilik.com/send_message_smtp.php \
  -d "name=Test&email=<EMAIL>&subject=genel&message=Test mesajı"
```

### Hangi Yöntemi Seçmeli?

**Basit mail() fonksiyonu:**
- ✅ Kolay kurulum
- ❌ Spam klasörüne düşebilir
- ❌ Bazı sunucularda çalışmaz

**PHPMailer + SMTP:**
- ✅ Güvenilir teslimat
- ✅ Spam klasörüne düşmez
- ✅ Hata yönetimi daha iyi
- ❌ Biraz daha karmaşık kurulum

### Canlıya Geçerken Yapılacaklar:

1. **Hosting sağlayıcınızdan SMTP bilgilerini alın**
2. **Mail hesapları oluşturun:**
   - `<EMAIL>` (ana hesap)
   - `<EMAIL>` (sistem maileri için)
3. **PHPMailer kullanacaksanız:**
   - `send_message_smtp.php` dosyasını kullanın
   - `contact.php` dosyasında form action'ını değiştirin
   - SMTP ayarlarını güncelleyin
4. **Test edin ve log dosyalarını kontrol edin**

### Sorun Giderme:

**Mail gitmiyor:**
- SMTP ayarlarını kontrol edin
- Firewall/port kontrolü yapın
- Mail hesabı şifresini doğrulayın

**Spam klasörüne düşüyor:**
- SPF, DKIM, DMARC kayıtları ekleyin
- Domain reputation kontrol edin

**Hata alıyorum:**
- PHP error loglarını kontrol edin
- SMTP debug modunu açın: `$mail->SMTPDebug = 2;`