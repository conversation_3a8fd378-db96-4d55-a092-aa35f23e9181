version: '3.8'

services:
  nginx:
    image: nginx:alpine
    container_name: asya_nginx
    ports:
      - "80:80"
    volumes:
      - .:/var/www/html
      - ./nginx/nginx.conf:/etc/nginx/conf.d/default.conf
    depends_on:
      - php
    networks:
      - asya_network

  php:
    image: php:8.2-fpm-alpine
    container_name: asya_php
    volumes:
      - .:/var/www/html
    working_dir: /var/www/html
    networks:
      - asya_network

networks:
  asya_network:
    driver: bridge