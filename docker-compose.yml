services:
  nginx:
    image: nginx:alpine
    container_name: meka_nginx
    ports:
      - "80:80"
    volumes:
      - .:/var/www/html
      - ./nginx/nginx.conf:/etc/nginx/conf.d/default.conf
    depends_on:
      - php
    networks:
      - meka_network

  php:
    image: php:8.2-fpm-alpine
    container_name: meka_php
    volumes:
      - .:/var/www/html
    working_dir: /var/www/html
    networks:
      - meka_network

networks:
  meka_network:
    driver: bridge