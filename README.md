# Asya Fresh Website - PHP + Docker Version

Bu proje, Asya Fresh firmasının orijinal HTML sitesinin PHP versiyonudur ve Docker konteynerları ile çalıştırılmaktadır.

## Kurulum ve Çalıştırma

### Gere<PERSON><PERSON>mler
- Docker
- Docker Compose

### Kurulum Adımları

1. Projeyi klonlayın veya indirin
2. <PERSON>je dizinine gidin:
   ```bash
   cd meka07
   ```

3. <PERSON><PERSON> konteyne<PERSON><PERSON> başlatın:
   ```bash
   docker-compose up -d
   ```

4. Tarayıcınızda `http://localhost` adresine gidin

### Konteyner Yönetimi

**Konteynerları başlatmak:**
```bash
docker-compose up -d
```

**Konteynerları durdurmak:**
```bash
docker-compose down
```

**Konteyner durumunu kontrol etmek:**
```bash
docker-compose ps
```

**Logları görüntü<PERSON>:**
```bash
docker-compose logs
```

## <PERSON><PERSON>

```
├── index.php              # Ana dosya
├── pages/                  # Sayfa dosyaları
│   ├── home.php
│   ├── about.php
│   ├── products.php
│   ├── logistics.php
│   ├── news.php
│   └── contact.php
├── send_message.php        # İletişim formu işleyici
├── docker-compose.yml     # Docker konfigürasyonu
├── nginx/
│   └── nginx.conf         # Nginx konfigürasyonu
├── css/                   # CSS dosyaları
├── js/                    # JavaScript dosyaları
├── images/                # Resim dosyaları
└── asya/                  # Orijinal site dosyaları
```

## Özellikler

- **Responsive tasarım**: Mobil ve masaüstü uyumlu
- **Modern PHP**: PHP 8.2 ile geliştirildi
- **Docker desteği**: Nginx + PHP-FPM konteynerları
- **İletişim formu**: Çalışan mesaj gönderme sistemi
- **SEO dostu**: Temiz URL yapısı
- **Güvenlik**: Nginx güvenlik başlıkları

## Sayfalar

1. **Anasayfa** (`?page=home`) - Firma tanıtımı ve öne çıkan ürünler
2. **Hakkımızda** (`?page=about`) - Firma bilgileri ve misyon/vizyon
3. **Ürünler** (`?page=products`) - Meyve ve sebze kataloğu
4. **Lojistik** (`?page=logistics`) - Lojistik hizmetleri ve tesisler
5. **Haberler** (`?page=news`) - Sektör haberleri ve duyurular
6. **İletişim** (`?page=contact`) - İletişim formu ve bilgiler

## Teknolojiler

- **Backend**: PHP 8.2
- **Web Server**: Nginx (Alpine)
- **Container**: Docker & Docker Compose
- **Frontend**: HTML5, CSS3, JavaScript (jQuery)

## Geliştirme

Kod değişikliklerini yapmak için dosyaları düzenleyip konteynerları yeniden başlatmanız yeterlidir:

```bash
docker-compose restart
```

CSS/JS değişiklikleri için tarayıcı cache'ini temizlemeyi unutmayın.

## İletişim

Proje ile ilgili sorularınız için:
- E-posta: <EMAIL>
- Instagram: @MekaMeyvecilik_official