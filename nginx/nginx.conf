server {
    listen 80;
    server_name localhost;
    root /var/www/html;
    index index.php index.html index.htm;

    # Log dosyaları
    access_log /var/log/nginx/access.log;
    error_log /var/log/nginx/error.log;

    # Güvenlik headers
    add_header X-Frame-Options "SAMEORIGIN" always;
    add_header X-XSS-Protection "1; mode=block" always;
    add_header X-Content-Type-Options "nosniff" always;
    add_header Referrer-Policy "no-referrer-when-downgrade" always;
    add_header Content-Security-Policy "default-src 'self' http: https: data: blob: 'unsafe-inline'" always;

    # Dosya boyutu limiti
    client_max_body_size 100M;

    # Ana lokasyon
    location / {
        try_files $uri $uri/ /index.php?$query_string;
    }

    # PHP dosyaları
    location ~ \.php$ {
        try_files $uri =404;
        fastcgi_split_path_info ^(.+\.php)(/.+)$;
        fastcgi_pass php:9000;
        fastcgi_index index.php;
        include fastcgi_params;
        fastcgi_param SCRIPT_FILENAME $document_root$fastcgi_script_name;
        fastcgi_param PATH_INFO $fastcgi_path_info;
        fastcgi_read_timeout 300;
    }

    # Statik dosyalar için cache
    location ~* \.(jpg|jpeg|png|gif|ico|css|js|pdf|txt|tar|gz|mp4|webm|ogv)$ {
        expires 1y;
        add_header Cache-Control "public, immutable";
        try_files $uri =404;
    }

    # Gizli dosyaları engelle
    location ~ /\. {
        deny all;
        access_log off;
        log_not_found off;
    }

    # Log dosyalarını engelle
    location ~ \.(log|sql|conf)$ {
        deny all;
        access_log off;
        log_not_found off;
    }

    # Favicon
    location = /favicon.ico {
        access_log off;
        log_not_found off;
    }

    # Robots.txt
    location = /robots.txt {
        access_log off;
        log_not_found off;
    }

    # Gzip sıkıştırma
    gzip on;
    gzip_vary on;
    gzip_min_length 1024;
    gzip_proxied expired no-cache no-store private auth;
    gzip_types
        text/plain
        text/css
        text/xml
        text/javascript
        application/javascript
        application/xml+rss
        application/json;
}