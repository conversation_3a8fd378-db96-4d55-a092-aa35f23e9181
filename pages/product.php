<?php
$slug = $_GET['slug'] ?? '';
$product = null;

// Find the product by slug
foreach($products as $key => $p) {
    if($p['slug'] === $slug) {
        $product = $p;
        $product_key = $key;
        break;
    }
}

// Redirect to products page if product not found
if(!$product) {
    header('Location: ?page=products');
    exit;
}
?>

<section id="main-slider" class="fullwidth clearfix">
    <div class="tp-banner-container">
        <div class="rev_slider fullwidthabanner">
            <ul style="margin: 0; padding: 0; list-style: none;">
                <li data-transition="zoomout">
                    <img src="images/<?= $product['image'] ?>" alt="<?= $product['name'] ?> - Meka Meyvecilik" class="rev-slidebg" />
                    <div class="tp-caption tp_text sps text-center">
                        <h2 class="item-title red-border no-margin text-capitalize">
                            <a href="#"><?= $product['name'] ?><br>Premium Kalite</a>
                        </h2>
                    </div>
                </li>
            </ul>
        </div>
    </div>
</section>

<section class="sub-page article clearfix">
    <div class="inner">
        <div class="container">
            <div class="row first-row">
                <div class="col-12">
                    <article class="text relative">
                        <h1 class="page-title green-border text-capitalize"><span><?= $product['name'] ?></span></h1>
                        <p><?= $product['description'] ?></p>
                    </article>
                </div>
            </div>
        </div>
    </div>
</section>

<section id="product-details" class="clearfix" style="padding: 60px 0;">
    <div class="container">
        <div class="row">
            <div class="col-6">
                <div class="product-image-large">
                    <img src="images/<?= $product['image'] ?>" alt="<?= $product['name'] ?>" style="width: 100%; border-radius: 12px; box-shadow: 0 8px 32px rgba(0,0,0,0.1);">
                </div>
            </div>
            <div class="col-6">
                <div class="product-specifications" style="padding-left: 40px;">
                    <h2 style="margin-bottom: 30px; color: #2c5f41;">Ürün Özellikleri</h2>

                    <div class="spec-item" style="margin-bottom: 20px; display: flex; align-items: center;">
                        <i class="fas fa-weight-hanging" style="font-size: 20px; color: #4CAF50; margin-right: 15px; width: 25px;"></i>
                        <div>
                            <strong>Ağırlık:</strong> <?= $product['weight'] ?>
                        </div>
                    </div>

                    <div class="spec-item" style="margin-bottom: 20px; display: flex; align-items: center;">
                        <i class="fas fa-box" style="font-size: 20px; color: #4CAF50; margin-right: 15px; width: 25px;"></i>
                        <div>
                            <strong>Ambalaj:</strong> <?= $product['packaging'] ?>
                        </div>
                    </div>

                    <div class="spec-item" style="margin-bottom: 20px; display: flex; align-items: center;">
                        <i class="fas fa-shipping-fast" style="font-size: 20px; color: #4CAF50; margin-right: 15px; width: 25px;"></i>
                        <div>
                            <strong>Minimum İhracat:</strong> <?= $product['min_export'] ?>
                        </div>
                    </div>

                    <div class="spec-item" style="margin-bottom: 20px; display: flex; align-items: center;">
                        <i class="fas fa-warehouse" style="font-size: 20px; color: #4CAF50; margin-right: 15px; width: 25px;"></i>
                        <div>
                            <strong>Yükleme Depoları:</strong> <?= $product['warehouses'] ?>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</section>

<section id="availability-calendar" class="clearfix" style="padding: 60px 0; background-color: #f8f9fa;">
    <div class="container">
        <div class="row">
            <div class="col-12">
                <h2 class="section-title text-center" style="margin-bottom: 40px;">Mevsimlik Müsaitlik Takvimi</h2>
                <div class="calendar-container" style="display: flex; justify-content: center; flex-wrap: wrap; gap: 10px;">
                    <?php
                    $all_months = ['Ocak', 'Şubat', 'Mart', 'Nisan', 'Mayıs', 'Haziran', 'Temmuz', 'Ağustos', 'Eylül', 'Ekim', 'Kasım', 'Aralık'];
                    foreach($all_months as $month):
                        $is_available = in_array($month, $product['available_months']);
                        $bg_color = $is_available ? '#4CAF50' : '#e0e0e0';
                        $text_color = $is_available ? '#fff' : '#999';
                    ?>
                    <div class="month-item" style="
                        padding: 12px 20px;
                        background-color: <?= $bg_color ?>;
                        color: <?= $text_color ?>;
                        border-radius: 25px;
                        font-weight: <?= $is_available ? 'bold' : 'normal' ?>;
                        min-width: 80px;
                        text-align: center;
                        transition: all 0.3s ease;
                    ">
                        <?= $month ?>
                    </div>
                    <?php endforeach; ?>
                </div>
                <p class="text-center" style="margin-top: 20px; color: #666;">
                    <i class="fas fa-info-circle" style="margin-right: 8px;"></i>
                    Yeşil renkli aylar ürünün müsait olduğu dönemleri gösterir
                </p>
            </div>
        </div>
    </div>
</section>

<section id="contact-inquiry" class="clearfix" style="padding: 60px 0;">
    <div class="container">
        <div class="row">
            <div class="col-12">
                <h2 class="section-title text-center" style="margin-bottom: 40px;">Bu Ürün İçin İletişime Geçin</h2>
                <div class="contact-options" style="display: flex; justify-content: center; gap: 30px; flex-wrap: wrap;">
                    <a href="mailto:<?= $company_info['email'] ?>?subject=<?= $product['name'] ?> Hakkında Bilgi Talebi"
                       class="contact-btn" style="
                        display: flex;
                        align-items: center;
                        padding: 15px 30px;
                        background: linear-gradient(135deg, #4CAF50, #45a049);
                        color: white;
                        text-decoration: none;
                        border-radius: 50px;
                        transition: all 0.3s ease;
                        box-shadow: 0 4px 15px rgba(76, 175, 80, 0.3);
                       ">
                        <i class="fas fa-envelope" style="margin-right: 10px; font-size: 18px;"></i>
                        E-posta Gönder
                    </a>

                    <a href="https://api.whatsapp.com/send?phone=<?= str_replace([' ', '(', ')', '-'], '', $company_info['phone']) ?>&text=Merhaba! <?= $product['name'] ?> hakkında bilgi almak istiyorum."
                       class="contact-btn" target="_blank" style="
                        display: flex;
                        align-items: center;
                        padding: 15px 30px;
                        background: linear-gradient(135deg, #25D366, #128C7E);
                        color: white;
                        text-decoration: none;
                        border-radius: 50px;
                        transition: all 0.3s ease;
                        box-shadow: 0 4px 15px rgba(37, 211, 102, 0.3);
                       ">
                        <i class="fab fa-whatsapp" style="margin-right: 10px; font-size: 18px;"></i>
                        WhatsApp
                    </a>
                </div>
            </div>
        </div>
    </div>
</section>

<section id="other-products" class="clearfix" style="padding: 60px 0; background-color: #f8f9fa;">
    <div class="container">
        <div class="row">
            <div class="col-12">
                <h2 class="section-title text-center" style="margin-bottom: 40px;">Diğer Ürünlerimiz</h2>
            </div>
        </div>
        <div class="row">
            <?php
            $other_products = array_filter($products, function($key) use($product_key) {
                return $key !== $product_key;
            }, ARRAY_FILTER_USE_KEY);
            $other_products = array_slice($other_products, 0, 4, true);

            foreach($other_products as $key => $other_product):
            ?>
            <div class="col-3" style="margin-bottom: 30px;">
                <div class="product-card" style="
                    border: 1px solid #e0e0e0;
                    border-radius: 12px;
                    overflow: hidden;
                    transition: all 0.3s ease;
                    cursor: pointer;
                    background: white;
                    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
                " onclick="location.href='?page=product&slug=<?= $other_product['slug'] ?>'">
                    <div class="product-image">
                        <img src="images/<?= $other_product['image'] ?>" alt="<?= $other_product['name'] ?>" style="width: 100%; height: 180px; object-fit: cover;">
                    </div>
                    <div class="product-info text-center" style="padding: 15px;">
                        <h4 style="margin-bottom: 10px; font-size: 16px; color: #333;"><?= $other_product['name'] ?></h4>
                        <a href="?page=product&slug=<?= $other_product['slug'] ?>" class="btn red small" style="text-decoration: none; font-size: 14px;">
                            <span>Detayları Gör</span>
                        </a>
                    </div>
                </div>
            </div>
            <?php endforeach; ?>
        </div>
    </div>
</section>

<style>
.contact-btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 6px 20px rgba(0,0,0,0.15);
}

.product-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 8px 25px rgba(0,0,0,0.15);
}

.month-item:hover {
    transform: scale(1.05);
}
</style>