<?php
$page = $_GET['page'] ?? 'home';
$lang = $_GET['lang'] ?? 'tr';

$pages = [
    'home' => 'Anasayfa',
    'about' => 'Hakkımızda',
    'products' => 'Ürünler',
    'logistics' => 'Lojistik',
    'news' => 'Ha<PERSON>ler',
    'contact' => 'İletişim'
];

$company_info = [
    'name' => 'Meka Meyvecilik',
    'title' => 'Türk Taze Meyve ve Sebze İhracatı',
    'email' => '<EMAIL>',
    'instagram' => 'MekaMeyvecilik',
    'phone' => '+90 546 802 19 91',
    'address' => 'Çanakkale, Türkiye'
];

$products = [
    'peach' => [
        'name' => 'Şeftali ve Nektarin',
        'image' => 'peach-and-nectarin-2-.jpg',
        'slug' => 'seftali-nektarin',
        'weight' => '200-300 gr',
        'packaging' => 'Plastik, ahşap, karton',
        'min_export' => '20 ton',
        'warehouses' => 'Çanakkale',
        'available_months' => ['Mayıs', 'Haziran', 'Temmuz', 'Ağustos', 'Eylül','Ekim'],
        'description' => 'Yüksek kaliteli şeftali ve nektarin çeşitlerimiz, modern tarım teknikleri ile yetiştirilir ve ihracat standartlarında paketlenir.'
    ],
    'cherry' => [
        'name' => 'Kiraz',
        'image' => 'cherry-2-.jpg',
        'slug' => 'kiraz',
        'weight' => '18-28 mm',
        'packaging' => '500g, 1kg, 2kg kutular',
        'min_export' => '15 ton',
        'warehouses' => 'Isparta ve Denizli',
        'available_months' => ['Mayıs', 'Haziran', 'Temmuz'],
        'description' => 'Premium kalite kiraz çeşitlerimiz Napoleon, Regina ve Sweetheart gibi dünya standartlarındaki çeşitlerden oluşur.'
    ],
    'pomegranate' => [
        'name' => 'Trabzon Hurması',
        'image' => 'trabzon-hurma.jpg',
        'slug' => 'nar',
        'weight' => '250-450 gr',
        'packaging' => 'Plastik, karton kutular',
        'min_export' => '25 ton',
        'warehouses' => 'Çanakkale',
        'available_months' => ['Eylül', 'Ekim', 'Kasım', 'Aralık'],
        'description' => 'Rojo Brillante ve Hachiya çeşitlerimiz ile antioksidan değeri yüksek, premium kalite Trabzon Hurması üretimi yapıyoruz.'
    ],
    'orange' => [
        'name' => 'Portakal',
        'image' => 'orange.jpg',
        'slug' => 'portakal',
        'weight' => '150-300 gr',
        'packaging' => '15kg karton kutular',
        'min_export' => '20 ton',
        'warehouses' => 'Antalya ve Mersin',
        'available_months' => ['Kasım', 'Aralık', 'Ocak', 'Şubat', 'Mart'],
        'description' => 'Valencia ve Washington çeşitlerimiz ile vitamin C açısından zengin, taze portakal üretimi.'
    ],
    'apricot' => [
        'name' => 'Kayısı',
        'image' => 'apricot.jpg',
        'slug' => 'kayisi',
        'weight' => '40-80 gr',
        'packaging' => '5kg ahşap, plastik kutular',
        'min_export' => '10 ton',
        'warehouses' => 'Çanakkale',
        'available_months' => ['Haziran', 'Temmuz', 'Ağustos'],
        'description' => 'Dünya kalitesinde Çanakkale kayısımız ile sağlık ve lezzet bir arada.'
    ],
    'fig' => [
        'name' => 'İncir',
        'image' => 'black-fig.jpg',
        'slug' => 'incir',
        'weight' => '50-120 gr',
        'packaging' => '1kg, 5kg plastik konteyner',
        'min_export' => '8 ton',
        'warehouses' => 'Çanakkale',
        'available_months' => ['Ağustos', 'Eylül', 'Ekim'],
        'description' => 'Siyah ve yeşil incir çeşitlerimiz ile doğal şeker ve lif kaynağı.'
    ]
];
?>
<!DOCTYPE html>
<html lang="tr" class="tr">
<head>
    <meta charset="utf-8" />
    <meta http-equiv="X-UA-Compatible" content="IE=Edge" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0, user-scalable=0" />
    <meta name="theme-color" content="#ededed" />
    <link href="mekafavicon.png" rel="icon" type="image/png" />

    <title><?= ucfirst($page) ?> - <?= $company_info['name'] ?> | <?= $company_info['title'] ?></title>

    <meta property="og:locale" content="tr" />
    <meta property="og:type" content="website" />
    <meta property="og:site_name" content="<?= $company_info['name'] ?> | <?= $company_info['title'] ?>" />
    <meta property="og:title" content="<?= ucfirst($page) ?> - <?= $company_info['name'] ?>" />

    <link href="css/normalize.css" rel="stylesheet" />
    <link href="css/revolution.css" rel="stylesheet" />
    <link href="css/owl.carousel.css" rel="stylesheet" />
    <link href="css/generic.css" rel="stylesheet" />
    <link href="css/responsive.css" rel="stylesheet" />
    <link href="css/icons.css" rel="stylesheet" />
    <link href="https://fonts.googleapis.com/css?family=Open+Sans:400,600,700,800&display=swap&subset=latin-ext" rel="stylesheet" />
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet" />

    <script>
        let site = {
            path: '',
            local_path: '',
            theme: '/',
            csrf_token: '<?= bin2hex(function_exists('random_bytes') ? random_bytes(20) : openssl_random_pseudo_bytes(20)) ?>',
            map_direction: 'Harita ve Yol Tarifi',
            ajax_loading: 'İşleniyor, lütfen bekleyin.',
            ajax_error: 'Bir hata oluştu.',
            ajax_warning: 'Lütfen gerekli alanları doldurun.',
            ajax_success: 'Mesajınız başarıyla gönderildi. Teşekkür ederiz.'
        };
    </script>
    <script src="js/jquery-3.5.1.min.js"></script>
</head>
<body>


    <div id="outer-wrap">
        <div id="inner-wrap">
            <div id="site-wrap">
                <header id="header" class="clearfix">
                    <div class="top-bar">
                        <div class="container">
                            <div class="row">
                                <div class="col-4"></div>
                                <div class="col-8">
                                    <div class="pull-right flex align-items-center">
                                        <div class="info">
                                            <div class="flex">
                                                <a href="mailto:<?= $company_info['email'] ?>" class="email"><i class="fas fa-envelope"></i> <?= $company_info['email'] ?></a>
                                                <span class="divider"></span>
                                                <a href="https://www.instagram.com/<?= $company_info['instagram'] ?>/" class="in" rel="external"><i class="fab fa-instagram"></i> /<?= $company_info['instagram'] ?></a>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="bottom-bar">
                        <div class="container">
                            <div class="row justify-content-between">
                                <div class="col-4 relative logo">
                                    <a href="?page=home" class="logo-link">
                                        <img src="images/mekalogo4.png" alt="<?= $company_info['name'] ?>" class="logo-image" />
                                    </a>
                                </div>

                                <div class="col-8 flex justify-content-end">
                                    <nav class="main-nav">
                                        <ul>
                                            <?php foreach($pages as $key => $value): ?>
                                            <li <?= $page == $key ? 'class="selected"' : '' ?>>
                                                <a href="?page=<?= $key ?>"><span><?= $value ?></span></a>
                                            </li>
                                            <?php endforeach; ?>
                                        </ul>
                                    </nav>

                                    <div class="nav-button" id="nav-button">
                                        <div class="hamburger">
                                            <span></span>
                                            <span></span>
                                            <span></span>
                                        </div>
                                        <div class="cross">
                                            <span></span>
                                            <span></span>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div id="navigation">
                        <nav class="mobile-nav flex flex-column align-items-center justify-content-center"><div></div></nav>
                    </div>
                </header>

                <?php
                switch($page) {
                    case 'home':
                        include 'pages/home.php';
                        break;
                    case 'about':
                        include 'pages/about.php';
                        break;
                    case 'products':
                        include 'pages/products.php';
                        break;
                    case 'product':
                        include 'pages/product.php';
                        break;
                    case 'logistics':
                        include 'pages/logistics.php';
                        break;
                    case 'news':
                        include 'pages/news.php';
                        break;
                    case 'contact':
                        include 'pages/contact.php';
                        break;
                    default:
                        include 'pages/home.php';
                }
                ?>

                <footer id="footer" class="clearfix">
                    <div class="inner">
                        <div class="container">
                            <div class="row align-items-center">
                                <div class="col-4 a">
                                    <a href="?page=home" class="footer-logo-link">
                                        <img src="images/mekalogo3.png" alt="<?= $company_info['name'] ?>" class="footer-logo-image" /><br>
                                    </a>
                                </div>
                                <div class="col-8 b">
                                    <div class="flex justify-content-end">
                                        <div class="copyright">
                                            <a href="https://www.instagram.com/<?= $company_info['instagram'] ?>/" class="in" rel="external"><i class="fab fa-instagram"></i> /<?= $company_info['instagram'] ?></a>
                                            <span>|</span> © <?= date('Y') ?> - mekameyvecilik.com
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </footer>

            </div>
        </div>
    </div>

    <script src="js/jquery-3.5.1.min.js"></script>
    <script src="js/jquery.special.bundle.min.js"></script>
    <script src="js/jquery.themepunch.tools.min.js"></script>
    <script src="js/jquery.themepunch.revolution.min.js"></script>
    <script src="js/jquery.owl.carousel.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/wow/1.2.0/wow.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/vanilla-lazyload/17.8.3/lazyload.min.js"></script>
    <script src="js/common.js"></script>
    <script src="js/init.js"></script>
    <script>
        jQuery(function ($) {
            $('.video-volume').on('click', function () {
                var video = $('#full-video video')
                $('i', this).toggleClass('fa-volume fa-volume-mute')
                video.prop('volume', 1).prop('muted', !video.prop('muted'))
            })
        })
    </script>

</body>
</html>