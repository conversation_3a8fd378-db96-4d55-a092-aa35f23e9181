<?php
$page = $_GET['page'] ?? 'home';
$lang = $_GET['lang'] ?? 'tr';

$pages = [
    'home' => 'Anasayfa',
    'about' => 'Hakkımızda',
    'products' => 'Ürünler',
    'logistics' => 'Lojistik',
    'news' => 'Ha<PERSON><PERSON>',
    'contact' => 'İletişim'
];

$company_info = [
    'name' => 'Asya Fresh',
    'title' => 'Türk Taze Meyve ve Sebze İhracatı',
    'email' => '<EMAIL>',
    'instagram' => 'mekameyvecilik_insta',
    'phone' => '+90 362 123 45 67',
    'address' => 'Samsun, Türkiye'
];

$products = [
    'pomegranate' => ['name' => 'Nar', 'image' => '1pome960-720.png'],
    'cherry' => ['name' => 'Kiraz', 'image' => 'cherry-2-.jpg'],
    'pear' => ['name' => 'Armut', 'image' => '1pear960-720.png'],
    'orange' => ['name' => 'Portakal', 'image' => 'orange.jpg'],
    'mandarin' => ['name' => 'Mandalina', 'image' => 'mandarin.jpg'],
    'lemon' => ['name' => 'Limon', 'image' => 'lemon2.jpg'],
    'grapefruit' => ['name' => 'Greyfurt', 'image' => 'grapefruit2.jpg'],
    'apricot' => ['name' => 'Kayısı', 'image' => 'apricot.jpg'],
    'peach' => ['name' => 'Şeftali', 'image' => 'peach-and-nectarin-2-.jpg'],
    'plum' => ['name' => 'Erik', 'image' => 'plums.jpg'],
    'fig' => ['name' => 'İncir', 'image' => 'black-fig.jpg'],
    'grape' => ['name' => 'Üzüm', 'image' => '1redglobe960-720.png'],
    'quince' => ['name' => 'Ayva', 'image' => '1quince960-720.png'],
    'tomato' => ['name' => 'Domates', 'image' => 'round.jpg'],
    'pepper' => ['name' => 'Biber', 'image' => 'capia.jpg'],
    'cucumber' => ['name' => 'Salatalık', 'image' => 'cornishon.jpg']
];
?>
<!DOCTYPE html>
<html lang="tr" class="tr">
<head>
    <meta charset="utf-8" />
    <meta http-equiv="X-UA-Compatible" content="IE=Edge" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0, user-scalable=0" />
    <meta name="theme-color" content="#ededed" />
    <link href="favicon.png" rel="icon" type="image/png" />

    <title><?= ucfirst($page) ?> - <?= $company_info['name'] ?> | <?= $company_info['title'] ?></title>

    <meta property="og:locale" content="tr" />
    <meta property="og:type" content="website" />
    <meta property="og:site_name" content="<?= $company_info['name'] ?> | <?= $company_info['title'] ?>" />
    <meta property="og:title" content="<?= ucfirst($page) ?> - <?= $company_info['name'] ?>" />

    <link href="css/normalize.css" rel="stylesheet" />
    <link href="css/revolution.css" rel="stylesheet" />
    <link href="css/owl.carousel.css" rel="stylesheet" />
    <link href="css/generic.css" rel="stylesheet" />
    <link href="css/responsive.css" rel="stylesheet" />
    <link href="css/icons.css" rel="stylesheet" />
    <link href="https://fonts.googleapis.com/css?family=Open+Sans:400,600,700,800&display=swap&subset=latin-ext" rel="stylesheet" />
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet" />

    <script>
        let site = {
            path: '',
            local_path: '',
            theme: '/',
            csrf_token: '<?= bin2hex(function_exists('random_bytes') ? random_bytes(20) : openssl_random_pseudo_bytes(20)) ?>',
            map_direction: 'Harita ve Yol Tarifi',
            ajax_loading: 'İşleniyor, lütfen bekleyin.',
            ajax_error: 'Bir hata oluştu.',
            ajax_warning: 'Lütfen gerekli alanları doldurun.',
            ajax_success: 'Mesajınız başarıyla gönderildi. Teşekkür ederiz.'
        };
    </script>
    <script src="js/jquery-3.5.1.min.js"></script>
</head>
<body>


    <div id="outer-wrap">
        <div id="inner-wrap">
            <div id="site-wrap">
                <header id="header" class="clearfix">
                    <div class="top-bar">
                        <div class="container">
                            <div class="row">
                                <div class="col-4"></div>
                                <div class="col-8">
                                    <div class="pull-right flex align-items-center">
                                        <div class="info">
                                            <div class="flex">
                                                <a href="mailto:<?= $company_info['email'] ?>" class="email"><i class="fas fa-envelope"></i> <?= $company_info['email'] ?></a>
                                                <span class="divider"></span>
                                                <a href="https://www.instagram.com/<?= $company_info['instagram'] ?>/" class="in" rel="external"><i class="fab fa-instagram"></i> /<?= $company_info['instagram'] ?></a>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="bottom-bar">
                        <div class="container">
                            <div class="row justify-content-between">
                                <div class="col-4 relative logo">
                                    <a href="?page=home" class="logo-link">
                                        <img src="images/meka2.jpg" alt="<?= $company_info['name'] ?>" class="logo-image" />
                                        <span class="logo-text"><?= $company_info['name'] ?></span>
                                    </a>
                                </div>

                                <div class="col-8 flex justify-content-end">
                                    <nav class="main-nav">
                                        <ul>
                                            <?php foreach($pages as $key => $value): ?>
                                            <li <?= $page == $key ? 'class="selected"' : '' ?>>
                                                <a href="?page=<?= $key ?>"><span><?= $value ?></span></a>
                                            </li>
                                            <?php endforeach; ?>
                                        </ul>
                                    </nav>

                                    <div class="nav-button" id="nav-button">
                                        <div class="hamburger">
                                            <span></span>
                                            <span></span>
                                            <span></span>
                                        </div>
                                        <div class="cross">
                                            <span></span>
                                            <span></span>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div id="navigation">
                        <nav class="mobile-nav flex flex-column align-items-center justify-content-center"><div></div></nav>
                    </div>
                </header>

                <?php
                switch($page) {
                    case 'home':
                        include 'pages/home.php';
                        break;
                    case 'about':
                        include 'pages/about.php';
                        break;
                    case 'products':
                        include 'pages/products.php';
                        break;
                    case 'logistics':
                        include 'pages/logistics.php';
                        break;
                    case 'news':
                        include 'pages/news.php';
                        break;
                    case 'contact':
                        include 'pages/contact.php';
                        break;
                    default:
                        include 'pages/home.php';
                }
                ?>

                <footer id="footer" class="clearfix">
                    <div class="inner">
                        <div class="container">
                            <div class="row align-items-center">
                                <div class="col-4 a">
                                    <a href="?page=home" class="footer-logo-link">
                                        <img src="images/meka2.jpg" alt="<?= $company_info['name'] ?>" class="footer-logo-image" />
                                        <span class="footer-logo-text"><?= $company_info['name'] ?></span>
                                    </a>
                                </div>
                                <div class="col-8 b">
                                    <div class="flex justify-content-end">
                                        <div class="copyright">
                                            <a href="https://www.instagram.com/<?= $company_info['instagram'] ?>/" class="in" rel="external"><i class="fab fa-instagram"></i> /<?= $company_info['instagram'] ?></a>
                                            <span>|</span> © <?= date('Y') ?> - mekameyvecilik.com
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </footer>

            </div>
        </div>
    </div>

    <script src="js/jquery-3.5.1.min.js"></script>
    <script src="js/jquery.special.bundle.min.js"></script>
    <script src="js/jquery.themepunch.tools.min.js"></script>
    <script src="js/jquery.themepunch.revolution.min.js"></script>
    <script src="js/jquery.owl.carousel.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/wow/1.2.0/wow.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/vanilla-lazyload/17.8.3/lazyload.min.js"></script>
    <script src="js/common.js"></script>
    <script src="js/init.js"></script>
    <script>
        jQuery(function ($) {
            $('.video-volume').on('click', function () {
                var video = $('#full-video video')
                $('i', this).toggleClass('fa-volume fa-volume-mute')
                video.prop('volume', 1).prop('muted', !video.prop('muted'))
            })
        })
    </script>

    <div id="fixed-buttons">
        <div>
            <a href="https://api.whatsapp.com/send?phone=<?= str_replace([' ', '(', ')', '-'], '', $company_info['phone']) ?>&text=Merhaba!" class="whatsapp" title="WhatsApp'tan yazın" target="_blank" rel="nofollow"></a>
        </div>
    </div>

</body>
</html>