/*!
 * ASYAFRESH.COM.TR - 10/06/2021
 * Web Design: Erdogan Cakmak / erdogancakmak.com
 * Front End Developer: Ferdi <PERSON> / ferditarakci.com
 */

var log=console.log.bind(console),clickType="click",downType="mousedown",upType="mouseup";navigator.userAgent.match(/(iP(ad|od|hone))/i)&&(downType=clickType="touchstart",upType="touchend");

/*!
 * jQuery Browser Plugin 0.1.0
 * https://github.com/gabceb/jquery-browser-plugin
 *
 * Original jquery-browser code Copyright 2005, 2015 jQuery Foundation, Inc. and other contributors
 * http://jquery.org/license
 *
 * Modifications Copyright 2015 Gabriel Cebrian
 * https://github.com/gabceb
 *
 * Released under the MIT license
 *
 * Date: 05-07-2015
 */
/*global window: false */
!function(e){"function"==typeof define&&define.amd?define(["jquery"],function(r){return e(r)}):"object"==typeof module&&"object"==typeof module.exports?module.exports=e(require("jquery")):e(window.jQuery)}(function(e){"use strict";function r(e){void 0===e&&(e=window.navigator.userAgent),e=e.toLowerCase();var r=/(edge)\/([\w.]+)/.exec(e)||/(opr)[\/]([\w.]+)/.exec(e)||/(chrome)[ \/]([\w.]+)/.exec(e)||/(iemobile)[\/]([\w.]+)/.exec(e)||/(version)(applewebkit)[ \/]([\w.]+).*(safari)[ \/]([\w.]+)/.exec(e)||/(webkit)[ \/]([\w.]+).*(version)[ \/]([\w.]+).*(safari)[ \/]([\w.]+)/.exec(e)||/(webkit)[ \/]([\w.]+)/.exec(e)||/(opera)(?:.*version|)[ \/]([\w.]+)/.exec(e)||/(msie) ([\w.]+)/.exec(e)||e.indexOf("trident")>=0&&/(rv)(?::| )([\w.]+)/.exec(e)||e.indexOf("compatible")<0&&/(mozilla)(?:.*? rv:([\w.]+)|)/.exec(e)||[],o=/(ipad)/.exec(e)||/(ipod)/.exec(e)||/(windows phone)/.exec(e)||/(iphone)/.exec(e)||/(kindle)/.exec(e)||/(silk)/.exec(e)||/(android)/.exec(e)||/(win)/.exec(e)||/(mac)/.exec(e)||/(linux)/.exec(e)||/(cros)/.exec(e)||/(playbook)/.exec(e)||/(bb)/.exec(e)||/(blackberry)/.exec(e)||[],i={},n={browser:r[5]||r[3]||r[1]||"",version:r[2]||r[4]||"0",versionNumber:r[4]||r[2]||"0",platform:o[0]||""};if(n.browser&&(i[n.browser]=!0,i.version=n.version,i.versionNumber=parseInt(n.versionNumber,10)),n.platform&&(i[n.platform]=!0),(i.android||i.bb||i.blackberry||i.ipad||i.iphone||i.ipod||i.kindle||i.playbook||i.silk||i["windows phone"])&&(i.mobile=!0),(i.cros||i.mac||i.linux||i.win)&&(i.desktop=!0),(i.chrome||i.opr||i.safari)&&(i.webkit=!0),i.rv||i.iemobile){n.browser="msie",i.msie=!0}if(i.edge){delete i.edge;n.browser="msedge",i.msedge=!0}if(i.safari&&i.blackberry){n.browser="blackberry",i.blackberry=!0}if(i.safari&&i.playbook){n.browser="playbook",i.playbook=!0}if(i.bb){var a="blackberry";n.browser=a,i[a]=!0}if(i.opr){n.browser="opera",i.opera=!0}if(i.safari&&i.android){n.browser="android",i.android=!0}if(i.safari&&i.kindle){n.browser="kindle",i.kindle=!0}if(i.safari&&i.silk){n.browser="silk",i.silk=!0}return i.name=n.browser,i.platform=n.platform,i}return window.jQBrowser=r(window.navigator.userAgent),window.jQBrowser.uaMatch=r,e&&(e.browser=window.jQBrowser),window.jQBrowser});

if ($.browser.msie && $.browser.versionNumber < 10) $('html').addClass('old-ie');


/*!
 * CSS Browser Selector 0.81
 * Originally written by Rafael Lima (http://rafael.adm.br)
 * http://rafael.adm.br/css_browser_selector
 * License: http://creativecommons.org/licenses/by/2.5/
 * Co-maintained by:
 * https://github.com/ridjohansen/css_browser_selector
 * https://github.com/delka/css_browser_selector
 * https://github.com/verbatim/css_browser_selector
 */
!function(){var d={ua:"",is:function(e){return RegExp(e,"i").test(d.ua)},version:function(e,t){for(var i=(t=t.replace(".","_")).indexOf("_"),n="";0<i;)n+=" "+e+t.substring(0,i),i=t.indexOf("_",i+1);return n+=" "+e+t},getBrowser:function(){var e="gecko",t="webkit",i="chrome",n="firefox",r="safari",o="opera",a=d.ua,s=d.is;return[!/opera|webtv/i.test(a)&&/msie\s(\d+)/.test(a)?"ie ie"+(/trident\/4\.0/.test(a)?"8":RegExp.$1):s("edge/")?"edge ie"+(/edge\/(\d+)\.(\d+)/.test(a)?RegExp.$1+" ie"+RegExp.$1+"_"+RegExp.$2:""):s("trident/")?"ie ie"+(/trident\/.+rv:(\d+)/i.test(a)?RegExp.$1:""):s("firefox/")?e+" "+n+(/firefox\/((\d+)(\.(\d+))(\.\d+)*)/.test(a)?" "+n+RegExp.$2+" "+n+RegExp.$2+"_"+RegExp.$4:""):s("gecko/")?e:s("opera")?o+(/version\/((\d+)(\.(\d+))(\.\d+)*)/.test(a)?" "+o+RegExp.$2+" "+o+RegExp.$2+"_"+RegExp.$4:/opera(\s|\/)(\d+)\.(\d+)/.test(a)?" "+o+RegExp.$2+" "+o+RegExp.$2+"_"+RegExp.$3:""):s("konqueror")?"konqueror":s("chrome")?t+" "+i+(/chrome\/((\d+)(\.(\d+))(\.\d+)*)/.test(a)?" "+i+RegExp.$2+(0<RegExp.$4?" "+i+RegExp.$2+"_"+RegExp.$4:""):""):s("iron")?t+" iron":s("applewebkit/")?t+" "+r+(/version\/((\d+)(\.(\d+))(\.\d+)*)/.test(a)?" "+r+RegExp.$2+" "+r+RegExp.$2+RegExp.$3.replace(".","_"):/ Safari\/(\d+)/i.test(a)?"419"==RegExp.$1||"417"==RegExp.$1||"416"==RegExp.$1||"412"==RegExp.$1?" "+r+"2_0":"312"==RegExp.$1?" "+r+"1_3":"125"==RegExp.$1?" "+r+"1_2":"85"==RegExp.$1?" "+r+"1_0":"":""):s("mozilla/")?e:""]},getPlatform:function(){var e="winphone",t="android",i="blackberry",n=d.ua,r=d.version,o=d.is;return[o("j2me")?"j2me":o("windows phone")?e+(/Windows Phone (\d+)(\.(\d+))+/i.test(n)?" "+e+RegExp.$1+" "+e+RegExp.$1+RegExp.$2.replace(".","_"):/Windows Phone OS (\d+)(\.(\d+))+/i.test(n)?" "+e+RegExp.$1+" "+e+RegExp.$1+RegExp.$2.replace(".","_"):""):o("blackberry")?i+(/Version\/(\d+)(\.(\d+)+)/i.test(n)?" "+i+RegExp.$1+" "+i+RegExp.$1+RegExp.$2.replace(".","_"):/Blackberry ?(([0-9]+)([a-z]?))[\/|;]/gi.test(n)?" "+i+RegExp.$2+(RegExp.$3?" "+i+RegExp.$2+RegExp.$3:""):""):o("android")?t+(/Version\/(\d+)(\.(\d+))+/i.test(n)?" "+t+RegExp.$1+" "+t+RegExp.$1+RegExp.$2.replace(".","_"):"")+(/Android (.+); (.+) Build/i.test(n)?" device_"+RegExp.$2.replace(/ /g,"_").replace(/-/g,"_"):""):o("ipad|ipod|iphone")?(/CPU( iPhone)? OS (\d+[_|\.]\d+([_|\.]\d+)*)/i.test(n)?"ios"+r("ios",RegExp.$2):"")+" "+(/(ip(ad|od|hone))/gi.test(n)?RegExp.$1:""):o("playbook")?"playbook":o("kindle|silk")?"kindle":o("playbook")?"playbook":o("mac")?"mac"+(/mac os x ((\d+)[.|_](\d+))/.test(n)?" mac"+RegExp.$2+" mac"+RegExp.$1.replace(".","_"):""):o("win")?"win"+(o("windows nt 10.0")?" win10":o("windows nt 6.3")?" win8_1":o("windows nt 6.2")?" win8":o("windows nt 6.1")?" win7":o("windows nt 6.0")?" vista":o("windows nt 5.2")||o("windows nt 5.1")?" win_xp":o("windows nt 5.0")?" win_2k":o("windows nt 4.0")||o("WinNT4.0")?" win_nt":""):o("freebsd")?"freebsd":o("x11|linux")?"linux":""]},getMobile:function(){return[(0,d.is)("android|mobi|mobile|j2me|iphone|ipod|ipad|blackberry|winphone|playbook|kindle|silk")?"mobile":""]},getIpadApp:function(){var e=d.is;return[e("ipad|iphone|ipod")&&!e("safari")?"ipad_app":""]},getLang:function(){var e=d.ua;return[/[; |\[](([a-z]{2})(\-[a-z]{2})?)[)|;|\]]/i.test(e)?("lang_"+RegExp.$2).replace("-","_")+(""!=RegExp.$3?(" lang_"+RegExp.$1).replace("-","_"):""):""]}};"undefined"==typeof html&&(html=document.documentElement);var s={width:(window.outerWidth||html.clientWidth)-15,height:window.outerHeight||html.clientHeight,screens:[0,768,980,1200],screenSize:function(){s.width=(window.outerWidth||html.clientWidth)-15,s.height=window.outerHeight||html.clientHeight;for(var e=s.screens,t=e.length,i=[];t--;)if(s.width>=e[t]){t&&i.push("minw_"+e[t]),t<=2&&i.push("maxw_"+(e[t+1]-1));break}return i},getOrientation:function(){return s.width<s.height?["orientation_portrait"]:["orientation_landscape"]},getInfo:function(){var e=[];return e=(e=e.concat(s.screenSize())).concat(s.getOrientation())},getPixelRatio:function(){var e=[],t=window.devicePixelRatio?window.devicePixelRatio:1;if(1<(t=parseInt(t))){e.push("retina_"+t+"x");for(var i=2;i<=t;i++)e.push("r"+i+"x");e.push("hidpi")}else e.push("no-hidpi");return e}},p={data:new Image,div:document.createElement("div"),isIeLessThan9:!1,getImg:function(){return p.data.src="data:image/gif;base64,R0lGODlhAQABAIAAAAAAAP///ywAAAAAAQABAAACAUwAOw==",p.div.innerHTML="\x3c!--[if lt IE 9]><i></i><![endif]--\x3e",p.isIeLessThan9=1==p.div.getElementsByTagName("i").length,p.data},checkSupport:function(){return 1!=p.data.width||1!=p.data.height||p.isIeLessThan9?["no-datauri"]:["datauri"]}};window.css_browser_selector=function(e,t){var i=document.documentElement,n=[];t=t||"",d.ua=e.toLowerCase(),n=(n=(n=(n=(n=(n=(n=(n=n.concat(d.getBrowser())).concat(d.getPlatform())).concat(d.getMobile())).concat(d.getIpadApp())).concat(d.getLang())).concat(["js"])).concat(s.getPixelRatio())).concat(s.getInfo());var r=function(){i.className=i.className.replace(/ ?orientation_\w+/g,"").replace(/ [min|max|cl]+[w|h]_\d+/g,""),i.className=i.className+" "+s.getInfo().join(" ")};window.addEventListener?(window.addEventListener("resize",r),window.addEventListener("orientationchange",r)):window.attachEvent&&window.attachEvent("onresize",r);var o=p.getImg();o.onload=o.onerror=function(){i.className+=" "+p.checkSupport().join(" ")};var a=i.className.split(/ /);return n=n.concat(a),Array.prototype.filter||(Array.prototype.filter=function(e){"use strict";if(null==this)throw new TypeError;var t=Object(this),i=t.length>>>0;if("function"!=typeof e)throw new TypeError;for(var n=[],r=2<=arguments.length?arguments[1]:void 0,o=0;o<i;o++)if(o in t){var a=t[o];e.call(r,a,o,t)&&n.push(a)}return n}),(n=n.filter(function(e){return"no-js"!==e&&e}))[0]=t?t+n[0]:n[0],i.className=n.join(" "+t),i.className}}();var css_browser_selector_ns=css_browser_selector_ns||"";css_browser_selector(navigator.userAgent,css_browser_selector_ns);



/*!
 * Copyright 2012, Ben Lin (http://dreamerslab.com/)
 * Licensed under the MIT License (LICENSE.txt).
 * Version: 1.0.18
 * Requires: jQuery >= 1.2.3
 */
!function(t){"function"==typeof define&&define.amd?define(["jquery"],t):t(jQuery)}(function(t){t.fn.addBack=t.fn.addBack||t.fn.andSelf,t.fn.extend({actual:function(e,n){if(!this[e])throw'$.actual => The jQuery method "'+e+'" you called does not exist';var i,a,o={absolute:!1,clone:!1,includeMargin:!1,display:"block"},d=t.extend(o,n),r=this.eq(0);if(!0===d.clone)i=function(){r=r.clone().attr("style","position: absolute !important; top: -1000 !important; ").appendTo("body")},a=function(){r.remove()};else{var l,s=[],u="";i=function(){l=r.parents().addBack().filter(":hidden"),u+="visibility: hidden !important; display: "+d.display+" !important; ",!0===d.absolute&&(u+="position: absolute !important; "),l.each(function(){var e=t(this),n=e.attr("style");s.push(n),e.attr("style",n?n+";"+u:u)})},a=function(){l.each(function(e){var n=t(this),i=s[e];void 0===i?n.removeAttr("style"):n.attr("style",i)})}}i();var c=/(outer)/.test(e)?r[e](d.includeMargin):r[e]();return a(),c}})});




/*!
 * jQuery ftAlert 1.0
 *
 * Copyright 2016 - Ferdi Tarakci
 * ferditarakci.com
 */
!function(l){"use strict";l.fn.ftAlert=function(s){var e=l.extend({text:"Üzgünüm! Bir hata oluştu.",cssClass:"loading",close:!1},s),t=l("<div />").addClass("ftAlert").addClass(e.cssClass);l("<div>"+e.text+"</div>").appendTo(t),e.close&&l('<a href="#" class="close">&times;</a>').prependTo(t),this.find(".ftAlert").remove(),this.append(t),this.on(clickType,".close",function(s){l(this).closest(".ftAlert").remove()})}}(jQuery);





/*!
  * smartresize.js
  * debouncing function from John Hann
  * http://unscriptable.com/index.php/2009/03/20/debouncing-javascript-methods/
 */
!function(a,b){var c=function(a,b,c){var d;return function(){function h(){c||a.apply(f,g),d=null}var f=this,g=arguments;d?clearTimeout(d):c&&a.apply(f,g),d=setTimeout(h,b||100)}};jQuery.fn[b]=function(a){return a?this.on("resize",c(a)):this.trigger(b)}}(jQuery,"smartresize");



/**
 * Copyright (c) 2007 Ariel Flesler - aflesler ○ gmail • com | https://github.com/flesler
 * Licensed under MIT
 * <AUTHOR> Flesler
 * @version 2.1.2
 */
;(function(f){"use strict";"function"===typeof define&&define.amd?define(["jquery"],f):"undefined"!==typeof module&&module.exports?module.exports=f(require("jquery")):f(jQuery)})(function($){"use strict";function n(a){return!a.nodeName||-1!==$.inArray(a.nodeName.toLowerCase(),["iframe","#document","html","body"])}function h(a){return $.isFunction(a)||$.isPlainObject(a)?a:{top:a,left:a}}var p=$.scrollTo=function(a,d,b){return $(window).scrollTo(a,d,b)};p.defaults={axis:"xy",duration:0,limit:!0};$.fn.scrollTo=function(a,d,b){"object"=== typeof d&&(b=d,d=0);"function"===typeof b&&(b={onAfter:b});"max"===a&&(a=9E9);b=$.extend({},p.defaults,b);d=d||b.duration;var u=b.queue&&1<b.axis.length;u&&(d/=2);b.offset=h(b.offset);b.over=h(b.over);return this.each(function(){function k(a){var k=$.extend({},b,{queue:!0,duration:d,complete:a&&function(){a.call(q,e,b)}});r.animate(f,k)}if(null!==a){var l=n(this),q=l?this.contentWindow||window:this,r=$(q),e=a,f={},t;switch(typeof e){case "number":case "string":if(/^([+-]=?)?\d+(\.\d+)?(px|%)?$/.test(e)){e= h(e);break}e=l?$(e):$(e,q);case "object":if(e.length===0)return;if(e.is||e.style)t=(e=$(e)).offset()}var v=$.isFunction(b.offset)&&b.offset(q,e)||b.offset;$.each(b.axis.split(""),function(a,c){var d="x"===c?"Left":"Top",m=d.toLowerCase(),g="scroll"+d,h=r[g](),n=p.max(q,c);t?(f[g]=t[m]+(l?0:h-r.offset()[m]),b.margin&&(f[g]-=parseInt(e.css("margin"+d),10)||0,f[g]-=parseInt(e.css("border"+d+"Width"),10)||0),f[g]+=v[m]||0,b.over[m]&&(f[g]+=e["x"===c?"width":"height"]()*b.over[m])):(d=e[m],f[g]=d.slice&& "%"===d.slice(-1)?parseFloat(d)/100*n:d);b.limit&&/^\d+$/.test(f[g])&&(f[g]=0>=f[g]?0:Math.min(f[g],n));!a&&1<b.axis.length&&(h===f[g]?f={}:u&&(k(b.onAfterFirst),f={}))});k(b.onAfter)}})};p.max=function(a,d){var b="x"===d?"Width":"Height",h="scroll"+b;if(!n(a))return a[h]-$(a)[b.toLowerCase()]();var b="client"+b,k=a.ownerDocument||a.document,l=k.documentElement,k=k.body;return Math.max(l[h],k[h])-Math.min(l[b],k[b])};$.Tween.propHooks.scrollLeft=$.Tween.propHooks.scrollTop={get:function(a){return $(a.elem)[a.prop]()}, set:function(a){var d=this.get(a);if(a.options.interrupt&&a._last&&a._last!==d)return $(a.elem).stop();var b=Math.round(a.now);d!==b&&($(a.elem)[a.prop](b),a._last=this.get(a))}};return p});




/*! WOW wow.js - v1.3.0 - 2016-10-04
* https://wowjs.uk
* Copyright (c) 2016 Thomas Grainger; Licensed MIT
*/

!function(a,b){if("function"==typeof define&&define.amd)define(["module","exports"],b);else if("undefined"!=typeof exports)b(module,exports);else{var c={exports:{}};b(c,c.exports),a.WOW=c.exports}}(this,function(a,b){"use strict";function c(a,b){if(!(a instanceof b))throw new TypeError("Cannot call a class as a function")}function d(a,b){return b.indexOf(a)>=0}function e(a,b){for(var c in b)if(null==a[c]){var d=b[c];a[c]=d}return a}function f(a){return/Android|webOS|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(a)}function g(a){var b=arguments.length<=1||void 0===arguments[1]?!1:arguments[1],c=arguments.length<=2||void 0===arguments[2]?!1:arguments[2],d=arguments.length<=3||void 0===arguments[3]?null:arguments[3],e=void 0;return null!=document.createEvent?(e=document.createEvent("CustomEvent"),e.initCustomEvent(a,b,c,d)):null!=document.createEventObject?(e=document.createEventObject(),e.eventType=a):e.eventName=a,e}function h(a,b){null!=a.dispatchEvent?a.dispatchEvent(b):b in(null!=a)?a[b]():"on"+b in(null!=a)&&a["on"+b]()}function i(a,b,c){null!=a.addEventListener?a.addEventListener(b,c,!1):null!=a.attachEvent?a.attachEvent("on"+b,c):a[b]=c}function j(a,b,c){null!=a.removeEventListener?a.removeEventListener(b,c,!1):null!=a.detachEvent?a.detachEvent("on"+b,c):delete a[b]}function k(){return"innerHeight"in window?window.innerHeight:document.documentElement.clientHeight}Object.defineProperty(b,"__esModule",{value:!0});var l,m,n=function(){function a(a,b){for(var c=0;c<b.length;c++){var d=b[c];d.enumerable=d.enumerable||!1,d.configurable=!0,"value"in d&&(d.writable=!0),Object.defineProperty(a,d.key,d)}}return function(b,c,d){return c&&a(b.prototype,c),d&&a(b,d),b}}(),o=window.WeakMap||window.MozWeakMap||function(){function a(){c(this,a),this.keys=[],this.values=[]}return n(a,[{key:"get",value:function(a){for(var b=0;b<this.keys.length;b++){var c=this.keys[b];if(c===a)return this.values[b]}}},{key:"set",value:function(a,b){for(var c=0;c<this.keys.length;c++){var d=this.keys[c];if(d===a)return this.values[c]=b,this}return this.keys.push(a),this.values.push(b),this}}]),a}(),p=window.MutationObserver||window.WebkitMutationObserver||window.MozMutationObserver||(m=l=function(){function a(){c(this,a),"undefined"!=typeof console&&null!==console&&(console.warn("MutationObserver is not supported by your browser."),console.warn("WOW.js cannot detect dom mutations, please call .sync() after loading new content."))}return n(a,[{key:"observe",value:function(){}}]),a}(),l.notSupported=!0,m),q=window.getComputedStyle||function(a){var b=/(\-([a-z]){1})/g;return{getPropertyValue:function(c){"float"===c&&(c="styleFloat"),b.test(c)&&c.replace(b,function(a,b){return b.toUpperCase()});var d=a.currentStyle;return(null!=d?d[c]:void 0)||null}}},r=function(){function a(){var b=arguments.length<=0||void 0===arguments[0]?{}:arguments[0];c(this,a),this.defaults={boxClass:"wow",animateClass:"animated",offset:0,mobile:!0,live:!0,callback:null,scrollContainer:null,resetAnimation:!0},this.animate=function(){return"requestAnimationFrame"in window?function(a){return window.requestAnimationFrame(a)}:function(a){return a()}}(),this.vendors=["moz","webkit"],this.start=this.start.bind(this),this.resetAnimation=this.resetAnimation.bind(this),this.scrollHandler=this.scrollHandler.bind(this),this.scrollCallback=this.scrollCallback.bind(this),this.scrolled=!0,this.config=e(b,this.defaults),null!=b.scrollContainer&&(this.config.scrollContainer=document.querySelector(b.scrollContainer)),this.animationNameCache=new o,this.wowEvent=g(this.config.boxClass)}return n(a,[{key:"init",value:function(){this.element=window.document.documentElement,d(document.readyState,["interactive","complete"])?this.start():i(document,"DOMContentLoaded",this.start),this.finished=[]}},{key:"start",value:function(){var a=this;if(this.stopped=!1,this.boxes=[].slice.call(this.element.querySelectorAll("."+this.config.boxClass)),this.all=this.boxes.slice(0),this.boxes.length)if(this.disabled())this.resetStyle();else for(var b=0;b<this.boxes.length;b++){var c=this.boxes[b];this.applyStyle(c,!0)}if(this.disabled()||(i(this.config.scrollContainer||window,"scroll",this.scrollHandler),i(window,"resize",this.scrollHandler),this.interval=setInterval(this.scrollCallback,50)),this.config.live){var d=new p(function(b){for(var c=0;c<b.length;c++)for(var d=b[c],e=0;e<d.addedNodes.length;e++){var f=d.addedNodes[e];a.doSync(f)}});d.observe(document.body,{childList:!0,subtree:!0})}}},{key:"stop",value:function(){this.stopped=!0,j(this.config.scrollContainer||window,"scroll",this.scrollHandler),j(window,"resize",this.scrollHandler),null!=this.interval&&clearInterval(this.interval)}},{key:"sync",value:function(){p.notSupported&&this.doSync(this.element)}},{key:"doSync",value:function(a){if("undefined"!=typeof a&&null!==a||(a=this.element),1===a.nodeType){a=a.parentNode||a;for(var b=a.querySelectorAll("."+this.config.boxClass),c=0;c<b.length;c++){var e=b[c];d(e,this.all)||(this.boxes.push(e),this.all.push(e),this.stopped||this.disabled()?this.resetStyle():this.applyStyle(e,!0),this.scrolled=!0)}}}},{key:"show",value:function(a){return this.applyStyle(a),a.className=a.className+" "+this.config.animateClass,null!=this.config.callback&&this.config.callback(a),h(a,this.wowEvent),this.config.resetAnimation&&(i(a,"animationend",this.resetAnimation),i(a,"oanimationend",this.resetAnimation),i(a,"webkitAnimationEnd",this.resetAnimation),i(a,"MSAnimationEnd",this.resetAnimation)),a}},{key:"applyStyle",value:function(a,b){var c=this,d=a.getAttribute("data-wow-duration"),e=a.getAttribute("data-wow-delay"),f=a.getAttribute("data-wow-iteration");return this.animate(function(){return c.customStyle(a,b,d,e,f)})}},{key:"resetStyle",value:function(){for(var a=0;a<this.boxes.length;a++){var b=this.boxes[a];b.style.visibility="visible"}}},{key:"resetAnimation",value:function(a){if(a.type.toLowerCase().indexOf("animationend")>=0){var b=a.target||a.srcElement;b.className=b.className.replace(this.config.animateClass,"").trim()}}},{key:"customStyle",value:function(a,b,c,d,e){return b&&this.cacheAnimationName(a),a.style.visibility=b?"hidden":"visible",c&&this.vendorSet(a.style,{animationDuration:c}),d&&this.vendorSet(a.style,{animationDelay:d}),e&&this.vendorSet(a.style,{animationIterationCount:e}),this.vendorSet(a.style,{animationName:b?"none":this.cachedAnimationName(a)}),a}},{key:"vendorSet",value:function(a,b){for(var c in b)if(b.hasOwnProperty(c)){var d=b[c];a[""+c]=d;for(var e=0;e<this.vendors.length;e++){var f=this.vendors[e];a[""+f+c.charAt(0).toUpperCase()+c.substr(1)]=d}}}},{key:"vendorCSS",value:function(a,b){for(var c=q(a),d=c.getPropertyCSSValue(b),e=0;e<this.vendors.length;e++){var f=this.vendors[e];d=d||c.getPropertyCSSValue("-"+f+"-"+b)}return d}},{key:"animationName",value:function(a){var b=void 0;try{b=this.vendorCSS(a,"animation-name").cssText}catch(c){b=q(a).getPropertyValue("animation-name")}return"none"===b?"":b}},{key:"cacheAnimationName",value:function(a){return this.animationNameCache.set(a,this.animationName(a))}},{key:"cachedAnimationName",value:function(a){return this.animationNameCache.get(a)}},{key:"scrollHandler",value:function(){this.scrolled=!0}},{key:"scrollCallback",value:function(){if(this.scrolled){this.scrolled=!1;for(var a=[],b=0;b<this.boxes.length;b++){var c=this.boxes[b];if(c){if(this.isVisible(c)){this.show(c);continue}a.push(c)}}this.boxes=a,this.boxes.length||this.config.live||this.stop()}}},{key:"offsetTop",value:function(a){for(;void 0===a.offsetTop;)a=a.parentNode;for(var b=a.offsetTop;a.offsetParent;)a=a.offsetParent,b+=a.offsetTop;return b}},{key:"isVisible",value:function(a){var b=a.getAttribute("data-wow-offset")||this.config.offset,c=this.config.scrollContainer&&this.config.scrollContainer.scrollTop||window.pageYOffset,d=c+Math.min(this.element.clientHeight,k())-b,e=this.offsetTop(a),f=e+a.clientHeight;return d>=e&&f>=c}},{key:"disabled",value:function(){return!this.config.mobile&&f(navigator.userAgent)}}]),a}();b["default"]=r,a.exports=b["default"]});








/**
 * Extend jquery with a scrollspy plugin.
 * This watches the window scroll and fires events when elements are scrolled into viewport.
 *
 * throttle() and getTime() taken from Underscore.js
 * https://github.com/jashkenas/underscore
 *
 * <AUTHOR> 2013 John Smart
 * @license https://raw.github.com/thesmart/jquery-scrollspy/master/LICENSE
 * @see https://github.com/thesmart
 * @version 0.1.2
 */

!function(t){var n=t(window),e=[],o=[],r=!1,l=0,i={top:0,right:0,bottom:0,left:0};function c(){++l;var r=n.scrollTop(),c=n.scrollLeft(),u=c+n.width(),f=r+n.height(),a=function(n,o,r,l){var i=t();return t.each(e,function(t,e){var c=e.offset().top,u=e.offset().left,f=u+e.width(),a=c+e.height();!(u>o||f<l||c>r||a<n)&&i.push(e)}),i}(r+i.top,u+i.right,f+i.bottom,c+i.left);t.each(a,function(t,n){"number"!=typeof n.data("scrollSpy:ticks")&&n.triggerHandler("scrollSpy:enter"),n.data("scrollSpy:ticks",l)}),t.each(o,function(t,n){var e=n.data("scrollSpy:ticks");"number"==typeof e&&e!==l&&(n.triggerHandler("scrollSpy:exit"),n.data("scrollSpy:ticks",null))}),o=a}function u(){n.trigger("scrollSpy:winSize")}var f=Date.now||function(){return(new Date).getTime()};function a(t,n,e){var o,r,l,i=null,c=0;e||(e={});var u=function(){c=!1===e.leading?0:f(),i=null,l=t.apply(o,r),o=r=null};return function(){var a=f();c||!1!==e.leading||(c=a);var s=n-(a-c);return o=this,r=arguments,s<=0?(clearTimeout(i),i=null,c=a,l=t.apply(o,r),o=r=null):i||!1===e.trailing||(i=setTimeout(u,s)),l}}t.scrollSpy=function(o,l){(o=t(o)).each(function(n,o){e.push(t(o))}),l=l||{throttle:100},i.top=l.offsetTop||0,i.right=l.offsetRight||0,i.bottom=l.offsetBottom||0,i.left=l.offsetLeft||0;var u=a(c,l.throttle||100),f=function(){t(document).ready(u)};return r||(n.on("scroll",f),n.on("resize",f),r=!0),setTimeout(f,0),o},t.winSizeSpy=function(e){return t.winSizeSpy=function(){return n},e=e||{throttle:100},n.on("resize",a(u,e.throttle||100))},t.fn.scrollSpy=function(n){return t.scrollSpy(t(this),n)}}(jQuery);




/*!
* Clamp.js 0.5.1
*
* Copyright 2011-2013, Joseph Schmitt http://joe.sh
* Released under the WTFPL license
* http://sam.zoy.org/wtfpl/
*/
(function(){window.$clamp=function(c,d){function s(a,b){n.getComputedStyle||(n.getComputedStyle=function(a,b){this.el=a;this.getPropertyValue=function(b){var c=/(\-([a-z]){1})/g;"float"==b&&(b="styleFloat");c.test(b)&&(b=b.replace(c,function(a,b,c){return c.toUpperCase()}));return a.currentStyle&&a.currentStyle[b]?a.currentStyle[b]:null};return this});return n.getComputedStyle(a,null).getPropertyValue(b)}function t(a){a=a||c.clientHeight;var b=u(c);return Math.max(Math.floor(a/b),0)}function x(a){return u(c)*
	a}function u(a){var b=s(a,"line-height");"normal"==b&&(b=1.2*parseInt(s(a,"font-size")));return parseInt(b)}function l(a){if(a.lastChild.children&&0<a.lastChild.children.length)return l(Array.prototype.slice.call(a.children).pop());if(a.lastChild&&a.lastChild.nodeValue&&""!=a.lastChild.nodeValue&&a.lastChild.nodeValue!=b.truncationChar)return a.lastChild;a.lastChild.parentNode.removeChild(a.lastChild);return l(c)}function p(a,d){if(d){var e=a.nodeValue.replace(b.truncationChar,"");f||(h=0<k.length?
	k.shift():"",f=e.split(h));1<f.length?(q=f.pop(),r(a,f.join(h))):f=null;m&&(a.nodeValue=a.nodeValue.replace(b.truncationChar,""),c.innerHTML=a.nodeValue+" "+m.innerHTML+b.truncationChar);if(f){if(c.clientHeight<=d)if(0<=k.length&&""!=h)r(a,f.join(h)+h+q),f=null;else return c.innerHTML}else""==h&&(r(a,""),a=l(c),k=b.splitOnChars.slice(0),h=k[0],q=f=null);if(b.animate)setTimeout(function(){p(a,d)},!0===b.animate?10:b.animate);else return p(a,d)}}function r(a,c){a.nodeValue=c+b.truncationChar}d=d||{};
	var n=window,b={clamp:d.clamp||2,useNativeClamp:"undefined"!=typeof d.useNativeClamp?d.useNativeClamp:!0,splitOnChars:d.splitOnChars||[".","-","\u2013","\u2014"," "],animate:d.animate||!1,truncationChar:d.truncationChar||"\u2026",truncationHTML:d.truncationHTML},e=c.style,y=c.innerHTML,z="undefined"!=typeof c.style.webkitLineClamp,g=b.clamp,v=g.indexOf&&(-1<g.indexOf("px")||-1<g.indexOf("em")),m;b.truncationHTML&&(m=document.createElement("span"),m.innerHTML=b.truncationHTML);var k=b.splitOnChars.slice(0),
	h=k[0],f,q;"auto"==g?g=t():v&&(g=t(parseInt(g)));var w;z&&b.useNativeClamp?(e.overflow="hidden",e.textOverflow="ellipsis",e.webkitBoxOrient="vertical",e.display="-webkit-box",e.webkitLineClamp=g,v&&(e.height=b.clamp+"px")):(e=x(g),e<=c.clientHeight&&(w=p(l(c),e)));return{original:y,clamped:w}}})();







/*!
 * @preserve FastClick: polyfill to remove click delays on browsers with touch UIs.
 *
 * @codingstandard ftlabs-jsv2
 * @copyright The Financial Times Limited [All Rights Reserved]
 * @license MIT License (see LICENSE.txt)
 */

/*jslint browser:true, node:true*/
/*global define, Event, Node*/

!function(){"use strict";function t(e,o){var i;if(o=o||{},this.trackingClick=!1,this.trackingClickStart=0,this.targetElement=null,this.touchStartX=0,this.touchStartY=0,this.lastTouchIdentifier=0,this.touchBoundary=o.touchBoundary||10,this.layer=e,this.tapDelay=o.tapDelay||200,this.tapTimeout=o.tapTimeout||700,!t.notNeeded(e)){for(var r=["onMouse","onClick","onTouchStart","onTouchMove","onTouchEnd","onTouchCancel"],a=this,c=0,s=r.length;c<s;c++)a[r[c]]=function(t,e){return function(){return t.apply(e,arguments)}}(a[r[c]],a);n&&(e.addEventListener("mouseover",this.onMouse,!0),e.addEventListener("mousedown",this.onMouse,!0),e.addEventListener("mouseup",this.onMouse,!0)),e.addEventListener("click",this.onClick,!0),e.addEventListener("touchstart",this.onTouchStart,!1),e.addEventListener("touchmove",this.onTouchMove,!1),e.addEventListener("touchend",this.onTouchEnd,!1),e.addEventListener("touchcancel",this.onTouchCancel,!1),Event.prototype.stopImmediatePropagation||(e.removeEventListener=function(t,n,o){var i=Node.prototype.removeEventListener;"click"===t?i.call(e,t,n.hijacked||n,o):i.call(e,t,n,o)},e.addEventListener=function(t,n,o){var i=Node.prototype.addEventListener;"click"===t?i.call(e,t,n.hijacked||(n.hijacked=function(t){t.propagationStopped||n(t)}),o):i.call(e,t,n,o)}),"function"==typeof e.onclick&&(i=e.onclick,e.addEventListener("click",function(t){i(t)},!1),e.onclick=null)}}var e=navigator.userAgent.indexOf("Windows Phone")>=0,n=navigator.userAgent.indexOf("Android")>0&&!e,o=/iP(ad|hone|od)/.test(navigator.userAgent)&&!e,i=o&&/OS 4_\d(_\d)?/.test(navigator.userAgent),r=o&&/OS [6-7]_\d/.test(navigator.userAgent),a=navigator.userAgent.indexOf("BB10")>0;t.prototype.needsClick=function(t){switch(t.nodeName.toLowerCase()){case"button":case"select":case"textarea":if(t.disabled)return!0;break;case"input":if(o&&"file"===t.type||t.disabled)return!0;break;case"label":case"iframe":case"video":return!0}return/\bneedsclick\b/.test(t.className)},t.prototype.needsFocus=function(t){switch(t.nodeName.toLowerCase()){case"textarea":return!0;case"select":return!n;case"input":switch(t.type){case"button":case"checkbox":case"file":case"image":case"radio":case"submit":return!1}return!t.disabled&&!t.readOnly;default:return/\bneedsfocus\b/.test(t.className)}},t.prototype.sendClick=function(t,e){var n,o;document.activeElement&&document.activeElement!==t&&document.activeElement.blur(),o=e.changedTouches[0],(n=document.createEvent("MouseEvents")).initMouseEvent(this.determineEventType(t),!0,!0,window,1,o.screenX,o.screenY,o.clientX,o.clientY,!1,!1,!1,!1,0,null),n.forwardedTouchEvent=!0,t.dispatchEvent(n)},t.prototype.determineEventType=function(t){return n&&"select"===t.tagName.toLowerCase()?"mousedown":"click"},t.prototype.focus=function(t){var e;o&&t.setSelectionRange&&0!==t.type.indexOf("date")&&"time"!==t.type&&"month"!==t.type?(e=t.value.length,t.setSelectionRange(e,e)):t.focus()},t.prototype.updateScrollParent=function(t){var e,n;if(!(e=t.fastClickScrollParent)||!e.contains(t)){n=t;do{if(n.scrollHeight>n.offsetHeight){e=n,t.fastClickScrollParent=n;break}n=n.parentElement}while(n)}e&&(e.fastClickLastScrollTop=e.scrollTop)},t.prototype.getTargetElementFromEventTarget=function(t){return t.nodeType===Node.TEXT_NODE?t.parentNode:t},t.prototype.onTouchStart=function(t){var e,n,r;if(t.targetTouches.length>1)return!0;if(e=this.getTargetElementFromEventTarget(t.target),n=t.targetTouches[0],o){if((r=window.getSelection()).rangeCount&&!r.isCollapsed)return!0;if(!i){if(n.identifier&&n.identifier===this.lastTouchIdentifier)return t.preventDefault(),!1;this.lastTouchIdentifier=n.identifier,this.updateScrollParent(e)}}return this.trackingClick=!0,this.trackingClickStart=t.timeStamp,this.targetElement=e,this.touchStartX=n.pageX,this.touchStartY=n.pageY,t.timeStamp-this.lastClickTime<this.tapDelay&&t.preventDefault(),!0},t.prototype.touchHasMoved=function(t){var e=t.changedTouches[0],n=this.touchBoundary;return Math.abs(e.pageX-this.touchStartX)>n||Math.abs(e.pageY-this.touchStartY)>n},t.prototype.onTouchMove=function(t){return!this.trackingClick||((this.targetElement!==this.getTargetElementFromEventTarget(t.target)||this.touchHasMoved(t))&&(this.trackingClick=!1,this.targetElement=null),!0)},t.prototype.findControl=function(t){return void 0!==t.control?t.control:t.htmlFor?document.getElementById(t.htmlFor):t.querySelector("button, input:not([type=hidden]), keygen, meter, output, progress, select, textarea")},t.prototype.onTouchEnd=function(t){var e,a,c,s,u,l=this.targetElement;if(!this.trackingClick)return!0;if(t.timeStamp-this.lastClickTime<this.tapDelay)return this.cancelNextClick=!0,!0;if(t.timeStamp-this.trackingClickStart>this.tapTimeout)return!0;if(this.cancelNextClick=!1,this.lastClickTime=t.timeStamp,a=this.trackingClickStart,this.trackingClick=!1,this.trackingClickStart=0,r&&(u=t.changedTouches[0],(l=document.elementFromPoint(u.pageX-window.pageXOffset,u.pageY-window.pageYOffset)||l).fastClickScrollParent=this.targetElement.fastClickScrollParent),"label"===(c=l.tagName.toLowerCase())){if(e=this.findControl(l)){if(this.focus(l),n)return!1;l=e}}else if(this.needsFocus(l))return t.timeStamp-a>100||o&&window.top!==window&&"input"===c?(this.targetElement=null,!1):(this.focus(l),this.sendClick(l,t),o&&"select"===c||(this.targetElement=null,t.preventDefault()),!1);return!(!o||i||!(s=l.fastClickScrollParent)||s.fastClickLastScrollTop===s.scrollTop)||(this.needsClick(l)||(t.preventDefault(),this.sendClick(l,t)),!1)},t.prototype.onTouchCancel=function(){this.trackingClick=!1,this.targetElement=null},t.prototype.onMouse=function(t){return!this.targetElement||(!!t.forwardedTouchEvent||(!t.cancelable||(!(!this.needsClick(this.targetElement)||this.cancelNextClick)||(t.stopImmediatePropagation?t.stopImmediatePropagation():t.propagationStopped=!0,t.stopPropagation(),t.preventDefault(),!1))))},t.prototype.onClick=function(t){var e;return this.trackingClick?(this.targetElement=null,this.trackingClick=!1,!0):"submit"===t.target.type&&0===t.detail||((e=this.onMouse(t))||(this.targetElement=null),e)},t.prototype.destroy=function(){var t=this.layer;n&&(t.removeEventListener("mouseover",this.onMouse,!0),t.removeEventListener("mousedown",this.onMouse,!0),t.removeEventListener("mouseup",this.onMouse,!0)),t.removeEventListener("click",this.onClick,!0),t.removeEventListener("touchstart",this.onTouchStart,!1),t.removeEventListener("touchmove",this.onTouchMove,!1),t.removeEventListener("touchend",this.onTouchEnd,!1),t.removeEventListener("touchcancel",this.onTouchCancel,!1)},t.notNeeded=function(t){var e,o,i;if(void 0===window.ontouchstart)return!0;if(o=+(/Chrome\/([0-9]+)/.exec(navigator.userAgent)||[,0])[1]){if(!n)return!0;if(e=document.querySelector("meta[name=viewport]")){if(-1!==e.content.indexOf("user-scalable=no"))return!0;if(o>31&&document.documentElement.scrollWidth<=window.outerWidth)return!0}}if(a&&(i=navigator.userAgent.match(/Version\/([0-9]*)\.([0-9]*)/))[1]>=10&&i[2]>=3&&(e=document.querySelector("meta[name=viewport]"))){if(-1!==e.content.indexOf("user-scalable=no"))return!0;if(document.documentElement.scrollWidth<=window.outerWidth)return!0}return"none"===t.style.msTouchAction||"manipulation"===t.style.touchAction||(!!(+(/Firefox\/([0-9]+)/.exec(navigator.userAgent)||[,0])[1]>=27&&(e=document.querySelector("meta[name=viewport]"))&&(-1!==e.content.indexOf("user-scalable=no")||document.documentElement.scrollWidth<=window.outerWidth))||"none"===t.style.touchAction||"manipulation"===t.style.touchAction)},t.attach=function(e,n){return new t(e,n)},"function"==typeof define&&"object"==typeof define.amd&&define.amd?define(function(){return t}):"undefined"!=typeof module&&module.exports?(module.exports=t.attach,module.exports.FastClick=t):window.FastClick=t}();jQuery(function($){FastClick.attach(document.body)});



/*!
 * By Osvaldas Valutis, www.osvaldas.info
 * Available for use under the MIT License
*/
;(function(e,t,n,r){e.fn.doubleTapToGo=function(r){if(!("ontouchstart"in t)&&!navigator.msMaxTouchPoints&&!navigator.userAgent.toLowerCase().match(/windows phone os 7/i))return false;this.each(function(){var t=false;e(this).on("click",function(n){var r=e(this);if(r[0]!=t[0]){n.preventDefault();t=r}});e(n).on("click touchstart MSPointerDown",function(n){var r=true,i=e(n.target).parents();for(var s=0;s<i.length;s++)if(i[s]==t[0])r=false;if(r)t=false})});return this}})(jQuery,window,document);
