/*!
 * MEKA MEYVECİLİK - 22/09/2025
 * Web Design: Virecus
 */

var lazy, RTL = false;

jQuery(function( $ ) {

	// RTL = $('html').hasClass('ar');

	bodyScrollWidth(); $(window).smartresize( bodyScrollWidth );

	$('#site-wrap > *, #header, #fixed-buttons').filter(function() {
		var a = $(this).css('position');
		if (a == 'fixed' || a == 'absolute') {
			$(this).wrapInner('<div class="fixed-fixes" />')
		}
	});

	$('a[rel~=external]').each(function() {
		$(this).attr('target', '_blank');
	});

	// $('#header .langs > span').on(clickType, function(e) {
	// 	var div = $(this).parent();
	// 	if (!div.is('.open')) {
	// 		div.addClass('open');
	// 	}
	// 	else {
	// 		div.removeClass('open');
	// 	}
	// });


	// $(document).on(downType, function(e) {
	// 	if ($(e.target).parents('.langs').length === 0 && $('#header .langs').is('.open')) $('#header .langs').removeClass('open');
	// });


	// FORMS
	// if ($().inputmask) {
	// 	$('.tcMask').inputmask({mask: '99999999999', showMaskOnHover: false});
	// 	$('.phoneMask').inputmask({mask: '0999-999-9999', showMaskOnHover: false});
	// 	$('.phoneMask2').inputmask({mask: '0999 999 9999', showMaskOnHover: false});
	// 	$('.emailMask').inputmask({casing: 'lower', showMaskOnHover: false, showMaskOnFocus: false, regex: '[a-z0-9\u0131\u0130._%+-]+@[a-z0-9\u0131\u0130-]+\.[a-z0-9-]{2,10}(\.[a-z0-9-]{2,10})?'});
	// 	$('.dateMask').inputmask('99/99/9999', {showMaskOnHover: false, clearIncomplete: true});
	// 	$('.dateMask2').inputmask('2099', {showMaskOnHover: false, clearIncomplete: true});
	// }

	$(document).on({
		hover: function() {
			$(this).removeAttr('placeholder');
		},
		focus: function() {
			$(this).addClass('focus');
			$(this).removeAttr('placeholder');
		},
		blur: function() {
			if ($(this).val() == '') $(this).removeClass('focus');
		}
	}, '.placeholder');

	$('.placeholder').each(function() {
		if ($(this).val() != '') {
			$(this).addClass('focus');
			$(this).removeAttr('placeholder');
		}
	});


	var mainNavList = $('#header .main-nav > ul').clone();
	$('#inner-wrap').prepend( $('#header') );
	$('#header .main-nav .selected').parents('li').addClass('selected');
	$('#header .main-nav li ul').parents('li').addClass('dropdown');


	var mainNavList = $('#header .main-nav > ul').clone();
	// mainNavSocials.find('a').removeClass('wow slideInUp').removeAttr('style data-wow-delay data-wow-offset');
	$('#header .mobile-nav > div').append(mainNavList.removeAttr('class'));

	var msOwl = $('<div class="owl-carousel" />').appendTo('#main-slider.home-slider');
	$('#main-slider.home-slider .rev_slider > ul > li').each(function() {
		var el = $(this);
		var link = el.attr('data-link') || '';
		var src = el.find('> img').attr('data-lazyload') || el.find('> img').attr('src');
		var title = el.find('> img').attr('alt') || '';
		var html = '';
		if (el.find('.tp_text').length) html = '<div class="owl-text' + (el.find('.tp_text').hasClass('hs') ? ' hs' : '') + '">'+ el.find('.tp_text').html() +'</div>';
		if (link != '') link = '<a href="' + link + '" class="overlay-link"></a>';

		msOwl.append('\
			<div class="item">\
				<span class="image' + ((el.find('.tp_text').length) ? '' : ' white-svg') + '">\
					<span>\
						<img alt="'+ title +'" data-src="'+ src +'" src="'+ site.theme +'images/a.gif" class="lazy" />\
					</span>\
				</span>\
				'+ html + link +'\
			</div>\
		');
	});

	

	lazyOfParent();
	// $(window).on("orientationchange", function() {
	// 	window.location.reload();
	// });

	var mainSlide = $('#main-slider .rev_slider');
	var mainSlideLen = $('> ul > li', mainSlide).length;

	if (mainSlideLen) {
		var mainSlideFullWidth = $('#main-slider').hasClass('fullwidth');
		var mainSlideFullWidth2 = $('#main-slider').hasClass('fullwidth2');
		var mainSlideFullScreen = !$('html').hasClass('orientation_portrait') && $('#main-slider').hasClass('fullscreen');

		mainSlide.find('> ul > li[data-link="#"]').removeAttr('data-link');
		var rev = mainSlide.show().revolution({
			sliderType: "standard",
			sliderLayout: (mainSlideFullScreen ? "fullscreen" : ((mainSlideFullWidth || mainSlideFullWidth2) ? "fullwidth" : "auto")),
			fullScreenOffsetContainer: "#header",
			dottedOverlay: "none",
			delay: 6000,
			navigation: {
				keyboardNavigation: "on",
				keyboard_direction: "horizontal",
				mouseScrollNavigation: "off",
				mouseScrollReverse: "default",
				onHoverStop: "on",
				touch: {
					touchenabled: "on",
					touchOnDesktop: "off",
					swipe_threshold: 75,
					swipe_min_touches: 1,
					swipe_direction: "horizontal",
					drag_block_vertical: false
				}

				,arrows: {
					style: "default",
					enable: true,
					hide_onmobile: true,
					// hide_over: 1200,
					// hide_under: 9999,
					hide_onleave: false,
					tmp: '',
					left: {
						container: "slider", // layergrid
						h_align: "left",
						v_align: "center",
						h_offset: 40,
						v_offset: -30
					},
					right: {
						container: "slider",
						h_align: "right",
						v_align: "center",
						h_offset: 40,
						v_offset: -30
					}
				}

				,bullets: {
					container: "slider",
					style: "round",
					enable: true,
					hide_onmobile: !1,
					hide_onleave: false,
					hide_delay: 200,
					hide_delay_mobile: 1200,
					hide_under: 0,
					hide_over: 9999,
					direction: "vertical",
					h_align: "right",
					v_align: "center",
					space: 0,
					h_offset: 20,
					v_offset: -50,
					tmp: ''
				}
			},

			viewPort: {
				enable: true,
				outof: "pause",
				visible_area: "80%",
				presize: false
			},

			responsiveLevels: [1200, 500, 768, 480],
			visibilityLevels: [1200, 500, 500, 500],
			gridwidth: [1200, 992, 768, 480],
			gridheight: (mainSlideFullScreen ? ($(window).height() - $('#header').height() - 100) : (mainSlideFullWidth ? 500 : 250)),

			shadow: 0,
			spinner: "off",
			stopLoop: "off",
			stopAfterLoops: -1,
			stopAtSlide: -1,
			shuffle: "off",
			autoHeight: "off",
			hideThumbsOnMobile: "off",
			hideSliderAtLimit: 0,
			hideCaptionAtLimit: 0,
			hideAllCaptionAtLilmit: 0,
			debugMode: false,
			fallbacks: {
				simplifyAll: "off",
				nextSlideOnWindowFocus: "off",
				disableFocusListener: false
			}
		});

		var hideTpArrow = function() {
			if (mainSlideLen < 2) {
				$('.tparrows', mainSlide).addClass('hide');
				$('.tp-bullets', mainSlide).addClass('hide');
			}
		}

		setTimeout(hideTpArrow, 1000);
		$(window).on('load', hideTpArrow);

		$('#main-slider .owl-carousel').owlCarousel({
			rtl: RTL,
			loop: true,
			autoplay: true,
			autoplayTimeout: 4000,
			autoplayHoverPause: false,
			autoplaySpeed: 600,
			smartSpeed: 600,
			margin: 0,
			nav: false,
			dots: true,
			items: 1
		});
	}




	var owlNavButton = ['<i class="fas fa-angle-left" aria-label="Previous"></i>','<i class="fas fa-angle-right" aria-label="Next"></i>'];

	$('#main-products .owl-carousel').owlCarousel({
		rtl: RTL,
		loop: false,
		autoplay: false,
		autoplayTimeout: 4000,
		autoplayHoverPause: true,
		autoplaySpeed: 600,
		smartSpeed: 600,
		margin: 0,
		nav: true,
		dots: true,
		navText: owlNavButton,
		responsive: {
			1100: {
				items: 3
			},
			840: {
				items: 3
			},
			576: {
				items: 2
			},
			320: {
				items: 1
			}
		}
	});

	$('#main-greenbox .owl-carousel').owlCarousel({
		rtl: RTL,
		loop: false,
		autoplay: false,
		autoplayTimeout: 4000,
		autoplayHoverPause: true,
		autoplaySpeed: 600,
		smartSpeed: 600,
		margin: 10,
		nav: true,
		dots: true,
		navText: owlNavButton,
		responsive: {
			1100: {
				items: 4
			},
			840: {
				items: 3
			},
			576: {
				items: 2
			},
			320: {
				items: 1
			}
		}
	});

	$('#main-news .owl-carousel').owlCarousel({
		rtl: RTL,
		loop: true,
		autoplay: true,
		autoplayTimeout: 4000,
		autoplayHoverPause: true,
		autoplaySpeed: 600,
		smartSpeed: 600,
		margin: 0,
		nav: false,
		dots: true,
		responsive: {
			1921: {
				items: 6
			},
			1600: {
				items: 5
			},
			1280: {
				items: 4
			},
			992: {
				items: 3
			},
			576: {
				items: 2
			},
			320: {
				items: 1
			}
		}
	});


	$('.products .images .owl-carousel').owlCarousel({
		loop: true,
		autoplay: true,
		autoplayTimeout: 4000,
		autoplayHoverPause: true,
		autoplaySpeed: 1000,
		smartSpeed: 800,
		lazyLoad: true,
		margin: 30,
		items: 1,
		nav: false,
		dots: true
	});

	if (typeof available_months == 'object') {
		$('.product-content .available div[data-month]').map(function() {
			var index = available_months.indexOf($(this).attr('data-month'));
			if (index > -1) $(this).addClass('selected');
		});
	}

	var wh = $('.whatsapp');
	if ($.browser.mobile && wh.length) {
		wh.each(function() {
			$(this).removeAttr('target').attr('href', $(this).attr('href').replace(/https:\/\/(web|api)\.whatsapp\.com\//i, 'whatsapp://'));
		});
	}

	var mf = $('#main-form');
	if (mf.length) {
		// $('select[data-name="service"]', mf).on('change', function() {
		// 	var val = $(this).val();
		// 	$('input[name="service"]', mf).val(val);

		// 	var urun = $('select[data-name="product"]', mf);
		// 	$('option[data-type]', urun).hide();
		// 	$('option[data-type="'+ val +'"]', urun).show();
		// });

		$('select[data-name="product"]', mf).on('change', function() {
			var val = $(this).val();
			$('input[name="product"]', mf).val(val);
		});
	}

	$('.side-nav').attr('data-lang', '');

	$('.side-nav .selected').parents('li').addClass('selected');

	var pagiNav = $('.pagination, .side-nav');
	if (pagiNav.length) {

		pagiNav.each(function() {
			var el = $(this);
			var pagiSelect = $('<select class="pagiselect" />').appendTo( this );
			pagiSelect.wrap('<label class="pagiselect-label" />');
			var optText = el.attr('data-lang');
			optText = (typeof optText === 'undefined') ? 'Sayfa' : optText;
			if (optText !== '') optText += ' ';
			$('a', el).each(function(a, b) {
				var opt = $('<option />', {
					value: $(this).attr('href'),
					text: optText + $(this).text()
				})
				.appendTo( pagiSelect );
				($(this).hasClass('selected') || $(this).parent('li').hasClass('selected')) && opt.prop('selected', true);
			});

			pagiSelect.on('change', function() {
				window.location = $(this).val();
			});
		});
	}




	$('select:not(.pagiselect):not(.ignore)').on('change', function() {
		var val = $(this).val();
		var val2 = $('option:eq(0)', this).val();
		var opt = $('option:selected', this).index();
		if ((val != '' && opt > 0)) {
			$(this).addClass('active-select');
		}
		else {
			$(this).removeClass('active-select');
		}
	}).trigger('change');





	function fixedHeader() {
		var sTop = $(window).scrollTop();
		if (ww() > 1200) {
			$('html').removeClass('mobile-fixed');

			if (sTop > 0) {
				$('html').addClass('fixed');
			}
			else {
				$('html').removeClass('fixed');
			}
		}
		else {
			$('html').removeClass('fixed');

			if (sTop > 5) {
				$('html').addClass('mobile-fixed');
			}
			else {
				$('html').removeClass('mobile-fixed');
			}
		}
	}

	fixedHeader();
	$(window).on('scroll resize', fixedHeader);




	var ios = navigator.userAgent.match(/(iP(ad|od|hone))/i);
	if (ios) $('a[href="#"]').doubleTapToGo(); else $(document).on(clickType, 'a[href="#"]', false);

	lazy = new LazyLoad({
		elements_selector: ".lazy",
		callback_loaded: function(el) {
			$(el).parent().addClass('loaded');
			videoLazyLoad(el);
		}
	});

	var wow, wowInit = false;
	wow = new WOW({
		animateClass: 'animated',
		offset: -10,
		callback: function(box) {
			$(window).trigger('scroll');
			videoLazyLoad(box);
		}
	});

	function preload_hide() {
		setTimeout(function() {
			if (!wowInit) {
				$('.ft-preload').fadeOut(600, function() { wow.init() });
				wowInit = true;
			}
		}, 50);
	}

	$(window).on('load', preload_hide);
	setTimeout(preload_hide, 4000);


	if ($('#map-div').length) {
		$('head').append('<script src="https://maps.googleapis.com/maps/api/js?language=' + $('html').attr('lang') + '&key=AIzaSyAVOj6If4GsGOKMhJHdQE-tW-Z9DKmX2QM&callback=initMap" type="text/javascript"></script>');
		$(window).one('load', function() {
			$('#map-div').ftGoogleMap();
		});
	}


	var resNav = $('#navigation');
	var mNav = false;
	var mNavLi = $('.mobile-nav > div > ul > li', resNav);
	var mNavDelay = (mNavLi.filter('.selected').length ? ((0.1 * mNavLi.length) + (260 * mNavLi.length)) : 1);

	function mainMobilNav() {
		$('.mobile-nav > div > ul > li', resNav).each(function() {
			var li = $(this);
			var ul = $('> ul', li);

			ul.length && li.addClass('dropdown');
			ul.css('height', 'auto');
			ul.attr('data-height', ul.actual('innerHeight')).height( 0 );

			ul.length && $('> a', li).removeAttr('href');
		});
	}

	mainMobilNav();

	resNav.on('click', '.mobile-nav > div > ul > li > a', function(e) {
		// e.preventDefault();
		// e.stopPropagation();

		// var el = $(this);
		// var id = el.attr('href') || el.attr('data-href');
		// var idpos = $(id).offset().top;

		// el.parent('li').addClass('selected').siblings().removeClass('selected');
	
		// $('#nav-button.open').trigger('click');
	
		// setTimeout(function() {
		// 	_scrollTo(idpos - $('#header').height(), 800);
		// }, 500);

		var el = $(this);
		var ul = el.parent('li').find('> ul');

		if (ul.length == 0) return;
		var parentUL = el.closest('ul');

		$('.mobile-nav > div > ul', resNav).css('overflow', '').find('> li > ul').not(ul).clearQueue().animate({'height' : 0}, 300, function() {
			$(this).hide();
			$(this).parent('li').removeClass('hover');
		});

		if (!ul.is(':visible')) {
			el.parent('li').addClass('hover');
			ul.show();
			parentUL.css('overflow', 'visible');
			ul.clearQueue().animate({'height' : parseInt(ul.attr('data-height'))}, 300, function() {
				ul.css('height', 'auto');
			});
		}
		else {
			ul.clearQueue().animate({height : 0}, 300, function() {
				el.parent('li').removeClass('hover');
				$(this).hide();
			});
		}

		return false;
	});


	function navHeight() {
		$('#site-wrap, #navigation').height('');

		if (resNav.hasClass('open')) {
			var siteWrapHeight = $('#site-wrap').actual('innerHeight');
			$('#site-wrap').height( siteWrapHeight );
		}
	}

	function navShowHide( e ) {

		if (!resNav.hasClass('open')) {

			$('html').addClass('htmlHidden');
			$('#navigation, #nav-button').addClass('open');

			navHeight();

			resNav.show().css('visibility', 'visible').animate({opacity: 1 }, 200, function() {
				$('html').addClass('open-nav');
				mNav = true;
				setTimeout(function() {
					resNav.find('.nav-button').addClass('open');
					!$('.mobile-nav > div > ul > li.selected', resNav).length && $('.mobile-nav li li.selected', resNav).parents('li').addClass('selected');
					$('.mobile-nav > div > ul > li.dropdown.selected > a').trigger('click');
					mNav = false;
				}, (mNav ? mNavDelay : 0));
			});
		}
		else {

			mNav = false;
			resNav.animate({ opacity: 0 }, 200, function() {
				resNav.hide().css('visibility', '');
				resNav.find('.nav-button').removeClass('open');

				$('html').removeClass('open-nav htmlHidden');
				$('#navigation, #nav-button').removeClass('open');
				$('#site-wrap, #navigation').removeAttr('style');
				var selected = $('.mobile-nav > div > ul > li.dropdown.selected', resNav);
				$('> a', selected).off( 'click' );
				$('> ul', selected).css({'height': 0, 'display': 'none'});

			});
		}

		e.preventDefault();
		e.stopPropagation();
	}

	$(document).on('click', '#nav-button', navShowHide);

	$(window).smartresize(function() {
		navHeight();
		if (ww() > 767) {
			$('#nav-button.open').trigger('click');
		}
	});

}); /* jQuery Ready */







jQuery(function( $ ) {

	$(document).on(clickType, '#captcha-refresh', function(e) {
		$.post(site.path + '/ajax/captcha/', {'csrf_token': site.csrf_token, 'ajax': true}, function(data) {
			$('#captcha-image').html( data );
			var image = $('#captcha-image img');
			image.attr('src', image.attr('data-src')).removeAttr('class alt data-src');
		});
	});

	$(document).on('change', 'input:file', function(e) {
		var input = $(this).next(':text');
		// if (!input.attr('data-text')) input.attr('data-text', input.text());
		var text = $(this).val().split('\\').pop();
		//if (text != '') text = 'C://fakepath/' + text;
		// if (text == '') text = span.attr('data-text');
		input.val( text );
	});

	//site.ajax_loading = 'İşlem yapılıyor, Lütfen bekleyin.';
	//site.ajax_error = 'Üzgünüm! Bir hata oluştu.';
	//site.ajax_warning = 'Lütfen gerekli alanları doldurunuz.';
	//site.ajax_success = 'Mesajınız başarı ile gönderilmiştir. Teşekkür ederiz.';

	$('[required]').parent('label, .form-label').append('<i class="required" />');
	$('textarea[required]').parent('.form-label').addClass('textarea');

	// phoneCountryCode();

	$(document).on('submit', 'form.ftAjaxForm', function(e) {
		e.preventDefault();
		var el = $(this);

		$(':submit', el).prop('disabled', true);

		setTimeout(function() {
			$(':submit', el).prop('disabled', false);
		}, 3000);

		$('input, select, textarea', el).parents('label, .form-label').removeClass('error');

		// var phone = $('input.phoneCountryCode', el);
		// //phone.trigger('keypress');
		// //log(phone.intlTelInput('isValidNumber'))
		// if(phone.length) {
		// 	//if(!phone.intlTelInput('isValidNumber')) {
		// 		//log(phone.intlTelInput('getSelectedCountryData'))
		// 		var phoneCountry = phone.intlTelInput('getSelectedCountryData');
		// 		var countryName = phoneCountry.name;
		// 		var countryShort = phoneCountry.iso2;
		// 		var phoneCode = phoneCountry.dialCode;
		// 		var phoneNumber = phone.intlTelInput('getNumber');
		// 		$('input[name="phone_country_name"]', el).val( countryName );
		// 		$('input[name="phone_country_code"]', el).val( countryShort );
		// 		$('input[name="phone_code"]', el).val( phoneCode );
		// 		$('input[name="phone_number"]', el).val( phoneNumber.substr(phoneCode.length + 1) );
		// 		$('input[name="phone_full_number"]', el).val( phoneNumber );
		// 	//}
		// }

		var _formData = new FormData(el[0]);
		_formData.append('ajax', true);
		var _mimeType = $(':file', el).length ? 'multipart/form-data' : false;

		$.ajax({
			type: el.attr('method'),
			url: el.attr('action'),
			data: _formData,
			mimeType: _mimeType,
			dataType: 'json',
			cache: false,
			contentType: false,
			processData: false,
			beforeSend: function () {

				el.ftAlert({
					text: (el.closest('#appointment').length ? site.ajax_appointment_loading : site.ajax_loading),
					cssClass: 'loading'
				});

			},
			error: function (jqXHR, textStatus, errorThrown) {

				el.ftAlert({
					text: site.ajax_error,
					cssClass: 'error'
				});

			},
			success: function (data, textStatus, jqXHR) {
				if (!data.success) {

					if (typeof data.errors == 'undefined') {
						$('.required').each(function(i, item) {
							if ($(this).val() == '') {
								$(this).parents('label, .form-label').addClass('error');
							}
						});
					}
					else {
						$.each(data.errors, function(i, item) {
							$('[name="' + i + '"], [name="' + i + '\[\]"]', el).parents('label, .form-label').addClass('error');
							$('select[data-name="' + i + '"]', el).parents('label, .form-label').addClass('error');
						});
					}

					el.ftAlert({
						text: data.message ? data.message : site.ajax_warning,
						cssClass: 'error'
					});

				}
				else if (data.success) {

					el.ftAlert({
						text: (el.closest('#appointment').length ? site.ajax_appointment_success : (data.message ? data.message : site.ajax_success)),
						cssClass: 'success'
					});

					var timeout = $('[name="timeout"]', el).length ? $('[name="timeout"]', el).val() : 3000;
					var redirect_url = $('[name="redirect_url"]', el);

					if (redirect_url.length == 1) {
						setTimeout(function() {
							window.location = redirect_url.val();
						}, timeout);
					}

					setTimeout(function() {
						if (redirect_url.length == 0) window.location.reload();
					}, timeout);
				}
			}
		});
	});
});








(function( $ ) {
	$.fn.ftGoogleMap = function() {
		$(this).each(function() {
			var mapID = $(this);
			if (mapID.length == 0) return false;
			var mapCoordinate = mapID.data('coordinate').replace(' ', '').split(',');
			var mapZoom = eval(mapID.data('zoom')) ? eval(mapID.data('zoom')) : 15;
			var mapAddress = mapID.data('address') ? mapID.data('address') : '';
			var mapIcon = mapID.data('icon') ? mapID.data('icon') : '';
			var mapSaturation = eval(mapID.data('saturation')) ? eval(mapID.data('saturation')) : 0;
			var map_position = new google.maps.LatLng(mapCoordinate[0], mapCoordinate[1]);
			var marker_position = new google.maps.LatLng(mapCoordinate[0], mapCoordinate[1]);
			var map;
			var marker;

			var styleArray = [
				{
				  featureType: 'all',
				  elementType: 'all',
				  stylers: [
					{ saturation: mapSaturation } // <-- THIS
				  ]
				}
			];

			var mapOptions = {
				zoom: mapZoom,
				scrollwheel: false,
				mapTypeId: google.maps.MapTypeId.ROADMAP,
				streetViewControl: true,
				center: map_position,
				styles: styleArray,
				fullscreenControl: true,
				zoomControl: true,
				mapTypeControl: true
			};

			map = new google.maps.Map(document.getElementById( mapID.attr('id') ), mapOptions);
			//map = new google.maps.Map(mapID, mapOptions);

			if (mapAddress) {
				var infowindow = new google.maps.InfoWindow({
					content: mapAddress,
					size: new google.maps.Size(150, 50)
				});
			}

			marker = new google.maps.Marker({
				map: map,
				icon: mapIcon,
				draggable: false,
				animation: google.maps.Animation.DROP,
				position: marker_position
			});

			google.maps.event.addListener(marker, 'click', toggleBounce);

			function toggleBounce() {
				if (marker.getAnimation() != null) {
					if (mapAddress) infowindow.close();
					//marker.setAnimation(null);
				} else {
					if (mapAddress) infowindow.open(map, marker);
					//marker.setAnimation(google.maps.Animation.BOUNCE);
				}
			}
		});
	};
})(jQuery);



(function( $ ) {
	$.fn.disableSelection = function() {
		return this
			.addClass('noSelect')
			.attr('unselectable', 'on')
			.on('selectstart', false);
	};
})(jQuery);


function _scrollTo(a, b, c) {
	$.scrollTo(a, b, {
		axis: 'y',
		offset: {
			top: c
		}
	});
}


function ww(a) {
	return a ? $(window).width() : ($(window).width() + parseInt($('body').attr('data-sw'))) ;
}


function bodyScrollWidth() {
	var ww1 = $(window).width();
	$('html, body').css('overflow', 'hidden');
	var ww2 = $(window).width();
	$('html, body').css('overflow', '');
	var sw = (ww2 - ww1);
	$('body').attr('data-sw', sw);
	!$('#style-sw').length && $('<style type="text/css" id="style-sw">').appendTo('head');
	 $('#style-sw').html('.htmlHidden, .htmlHidden .fixed-fixes, .compensate-for-scrollbar .fixed-fixes {margin-right:' + sw + 'px !important;}.compensate-for-scrollbar #fix-buttons > div {margin-left:-' + sw + 'px !important;}');
}


function lazyOfParent() {
	$('img.owl-lazy, img.lazy, img.to-bg').not('.loaded').each(function() {
		var image = $(this);
		var span = image.closest('.image').find('> span');
		if (span.length) {
			if (image.hasClass('lazy')) {
				span.addClass('lazy');
				if (typeof image.data('src') !== 'undefined') span.attr('data-bg', image.attr('data-src')); else image.removeAttr('data-src');
				if (typeof image.data('srcset') !== 'undefined') span.attr('data-bg-hidpi', image.attr('data-srcset').replace(/ 2x| 3x/i, '')); else image.removeAttr('data-srcset');
			}
			else if (image.hasClass('owl-lazy')) {
				span.addClass('owl-lazy');
				span.attr('data-src', image.data('src'));
				if (typeof image.attr('data-src-retina') !== 'undefined') span.attr('data-src-retina', image.data('src-retina')); else image.removeAttr('data-src-retina');
			}
			else if (image.hasClass('to-bg')) {
				span.css('background-image', 'url(\'' + image.attr('src') + '\')');
			}

			image.addClass('loaded');
			span.parent('.image').addClass('loaded');
		}
	});
}


function videoLazyLoad(el) {
	var video = $(el).closest('.video');
	if (video.length) {
		var video = video.find('> div');
		var link = video.find('a');
		if (link.length) {
			var video_id = link.attr('href').match(/(?:youtube(?:-nocookie)?\.com\/(?:[^\/\n\s]+\/\S+\/|(?:v|e(?:mbed)?)\/|\S*?[?&]v=)|youtu\.be\/)([a-zA-Z0-9_-]{11})/)[1];
			video.html('<iframe width="640" height="360" src="https://www.youtube.com/embed/' + video_id + '" frameborder="0" allow="accelerometer; autoplay; encrypted-media; gyroscope; picture-in-picture" allowfullscreen></iframe>');
		}
	}
}



function _triggerResize() {
	$(window).trigger('resize');
}

jQuery(function( $ ) {
	$('[data-svg]').each(function() {
		var icon = $(this);
		var svgHref = icon.data('svg');
		var remove = icon.data('remove') || 1;
		$.get(svgHref, function(data) {
			var svg = document.importNode(data.documentElement, true);
			icon.append(svg);
			(remove != 'no') && icon.find('style').remove();
		})
	});

	_triggerResize();
	$(window).on('load', _triggerResize );

	var cp = $('#cookie-policy');
	if (cp.length) {
		localStorage.cookiePolicy ? cp.addClass('hide') : cp.removeClass('hide');

		cp.on(clickType, '.cookie-accept, .cookie-close', function() {
			if($(this).hasClass('cookie-accept')) localStorage.setItem('cookiePolicy', !1);
			cp.addClass('hide');
			$('body').css('paddingBottom', '');
		});

		$(window).on('resize', function() {
			!cp.hasClass('hide') && $('body').css('paddingBottom', cp.innerHeight());
		})
		.trigger('resize');
	}
});