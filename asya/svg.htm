<!DOCTYPE html>
<html lang="en">
<head>
  <title>SVG namespace</title>
  <meta http-equiv="Content-Type" content="text/html; charset=utf-8"/>
  <link rel="stylesheet" type="text/css"
        href="https://www.w3.org/StyleSheets/TR/base"/>
</head>
<body>
<div class="head">
<p><a href="https://www.w3.org/"><img class="head"
src="https://www.w3.org/assets/logos/w3c/w3c-no-bars.svg" alt="W3C"/></a></p>
</div>
<p>
<strong>http://www.w3.org/2000/svg</strong> is an XML namespace, first defined in the 
Scalable Vector Graphics (SVG) 1.0 Specification and subsequently added to by SVG 1.1, SVG 1.2 and SVG 2. The SVG namespace is  <a href="https://www.w3.org/2001/tag/doc/namespaceState.html"><em>mutable</em></a>; names may be added over time by the W3C SVG Working Group by publication in W3C Technical Reports. </p>
  
<p>The
latest version of the SVG specification can be found <a href="https://www.w3.org/TR/SVG/">https://www.w3.org/TR/SVG/</a>.
</p>

<p>
For more information about SVG, please refer to <a href="https://www.w3.org/Graphics/SVG/">the W3C SVG overview</a>.
For more information about XML, please refer to <a
href="https://www.w3.org/TR/xml/">The Extensible Markup Language
(XML) 1.0 specification</a>. For more information about XML
namespaces, please refer to the
<a href="https://www.w3.org/TR/xml-names/">Namespaces in XML
specification</a>.</p>

<hr />
<address>
  <a href="https://www.w3.org/help/">Webmaster</a><br />
  Last modified: $Date: 2025/03/14 23:44:06 $
</address>
</body>
</html>
