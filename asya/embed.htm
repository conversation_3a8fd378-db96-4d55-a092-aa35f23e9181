<!DOCTYPE html>
<html>
  <head>
    <style type="text/css">
      html, body, #mapDiv {
        height: 100%;
        margin: 0;
        padding: 0;
      }
    </style>
  </head>
  <body>
    <div id="mapDiv"></div>
      
    <script nonce="oLd0HwwzSW463irRkmhcYQ">
      function onEmbedLoad() {
        initEmbed([null,null,null,null,null,[],null,["tr","tr"],[null,null,null,"/maps/api/js/ApplicationService.GetEntityDetails","/maps/embed/upgrade204",null,"/maps/embed/record204"],null,null,null,null,null,null,null,null,"K7_PaIPtFtHoi-gP7vuBoAY",null,null,null,[[[2997.096940596964,36.33043921587894,41.30675467927161],[0,0,0],null,13.10000038146973],null,0],null,null,null,0,null,null,null,null,null,null,[1]]);
      }
      function onApiLoad() {
        var embed = document.createElement('script');
        embed.src = "https://maps.gstatic.com/maps-api-v3/embed/js/62/6d/intl/tr_ALL/init_embed.js";
        document.body.appendChild(embed);
      }
    </script>
    <link rel="preload" href="https://maps.gstatic.com/maps-api-v3/embed/js/62/6d/intl/tr_ALL/init_embed.js" nonce="oLd0HwwzSW463irRkmhcYQ" as="script" />
    <script src="https://maps.googleapis.com/maps/api/js?client=google-maps-embed&amp;paint_origin=&amp;libraries=geometry,search&amp;v=weekly&amp;loading=async&amp;language=tr&amp;region=tr&amp;callback=onApiLoad" nonce="oLd0HwwzSW463irRkmhcYQ" async defer></script>
  </body>
</html>
