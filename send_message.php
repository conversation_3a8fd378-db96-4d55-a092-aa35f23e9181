<?php
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $name = $_POST['name'] ?? '';
    $email = $_POST['email'] ?? '';
    $phone = $_POST['phone'] ?? '';
    $company = $_POST['company'] ?? '';
    $subject = $_POST['subject'] ?? '';
    $message = $_POST['message'] ?? '';

    // Basit validation
    if (empty($name) || empty($email) || empty($subject) || empty($message)) {
        $error = "Lütfen zorunlu alanları doldurun.";
    } elseif (!filter_var($email, FILTER_VALIDATE_EMAIL)) {
        $error = "Geçerli bir e-posta adresi girin.";
    } else {
        // Mesajı dosyaya kaydet (gerçek uygulamada veritabanına veya e-posta ile gönderilir)
        $log_entry = [
            'date' => date('Y-m-d H:i:s'),
            'name' => $name,
            'email' => $email,
            'phone' => $phone,
            'company' => $company,
            'subject' => $subject,
            'message' => $message
        ];

        $log_file = 'messages.log';
        file_put_contents($log_file, json_encode($log_entry) . "\n", FILE_APPEND);

        $success = "Mesajınız başarıyla gönderildi. En kısa sürede sizinle iletişime geçeceğiz.";
    }
}

$subject_options = [
    'urun_bilgisi' => 'Ürün Bilgisi',
    'fiyat_teklifi' => 'Fiyat Teklifi',
    'ihracat' => 'İhracat İşbirliği',
    'lojistik' => 'Lojistik Hizmetleri',
    'genel' => 'Genel Bilgi'
];
?>
<!DOCTYPE html>
<html lang="tr">
<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Mesaj Gönderildi - Asya Fresh</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 50px; background: #f5f5f5; }
        .container { max-width: 600px; margin: 0 auto; background: white; padding: 40px; border-radius: 8px; box-shadow: 0 2px 10px rgba(0,0,0,0.1); }
        .success { color: #4CAF50; background: #E8F5E8; padding: 15px; border-radius: 4px; margin-bottom: 20px; }
        .error { color: #F44336; background: #FFEBEE; padding: 15px; border-radius: 4px; margin-bottom: 20px; }
        .btn { display: inline-block; background: #4CAF50; color: white; padding: 12px 24px; text-decoration: none; border-radius: 4px; margin-top: 20px; }
        .btn:hover { background: #45a049; }
    </style>
</head>
<body>
    <div class="container">
        <h1>Asya Fresh - İletişim</h1>

        <?php if (isset($success)): ?>
            <div class="success">
                <strong>Başarılı!</strong> <?= $success ?>
            </div>
            <p>Mesaj detayları:</p>
            <ul>
                <li><strong>Ad Soyad:</strong> <?= htmlspecialchars($name) ?></li>
                <li><strong>E-posta:</strong> <?= htmlspecialchars($email) ?></li>
                <?php if ($phone): ?><li><strong>Telefon:</strong> <?= htmlspecialchars($phone) ?></li><?php endif; ?>
                <?php if ($company): ?><li><strong>Firma:</strong> <?= htmlspecialchars($company) ?></li><?php endif; ?>
                <li><strong>Konu:</strong> <?= $subject_options[$subject] ?? $subject ?></li>
                <li><strong>Mesaj:</strong> <?= nl2br(htmlspecialchars($message)) ?></li>
            </ul>
        <?php elseif (isset($error)): ?>
            <div class="error">
                <strong>Hata!</strong> <?= $error ?>
            </div>
        <?php endif; ?>

        <a href="index.php" class="btn">Ana Sayfaya Dön</a>
        <a href="index.php?page=contact" class="btn">İletişim Sayfasına Dön</a>
    </div>
</body>
</html>