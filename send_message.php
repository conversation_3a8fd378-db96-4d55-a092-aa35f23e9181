<?php
// Hata raporlamasını kapat (production için)
error_reporting(0);
ini_set('display_errors', 0);

// Form verilerini kontrol et
if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    header('Location: ?page=contact');
    exit;
}

// Form verilerini al ve temizle
$name = trim($_POST['name'] ?? '');
$email = trim($_POST['email'] ?? '');
$phone = trim($_POST['phone'] ?? '');
$company = trim($_POST['company'] ?? '');
$subject = trim($_POST['subject'] ?? '');
$message = trim($_POST['message'] ?? '');

// Zorunlu alanları kontrol et
if (empty($name) || empty($email) || empty($subject) || empty($message)) {
    header('Location: ?page=contact&error=required_fields');
    exit;
}

// E-posta adresini doğrula
if (!filter_var($email, FILTER_VALIDATE_EMAIL)) {
    header('Location: ?page=contact&error=invalid_email');
    exit;
}

// Konu başlıklarını Türkçe'ye çevir
$subject_titles = [
    'urun_bilgisi' => 'Ürün Bilgisi',
    'fiyat_teklifi' => 'Fiyat Teklifi',
    'ihracat' => 'İhracat İşbirliği',
    'lojistik' => 'Lojistik Hizmetleri',
    'genel' => 'Genel Bilgi'
];

$subject_title = $subject_titles[$subject] ?? 'Genel Bilgi';

// Mail ayarları
$to = '<EMAIL>'; // Sizin mail adresiniz
$mail_subject = "Meka Meyvecilik - $subject_title Talebi - $name";

// Mail içeriği hazırla
$mail_body = "
MEKA MEYVECİLİK WEB SİTESİ İLETİŞİM FORMU
==========================================

Gönderen Bilgileri:
- Ad Soyad: $name
- E-posta: $email
- Telefon: " . ($phone ?: 'Belirtilmemiş') . "
- Firma: " . ($company ?: 'Belirtilmemiş') . "

Konu: $subject_title

Mesaj:
$message

==========================================
Gönderim Tarihi: " . date('d.m.Y H:i:s') . "
IP Adresi: " . ($_SERVER['REMOTE_ADDR'] ?? 'Bilinmiyor') . "
";

// Mail başlıkları
$headers = [
    'From: Meka Meyvecilik Website <<EMAIL>>',
    'Reply-To: ' . $email,
    'Content-Type: text/plain; charset=UTF-8',
    'X-Mailer: PHP/' . phpversion()
];

// Mail gönder
$mail_sent = mail($to, $mail_subject, $mail_body, implode("\r\n", $headers));

// Gönderici adına otomatik yanıt maili gönder
$auto_reply_subject = "Meka Meyvecilik - Mesajınız Alındı";
$auto_reply_body = "
Sayın $name,

Meka Meyvecilik ile iletişime geçtiğiniz için teşekkür ederiz.

Mesajınız başarıyla alınmıştır ve en kısa sürede size geri dönüş yapacağız.

Mesaj Özeti:
- Konu: $subject_title
- Tarih: " . date('d.m.Y H:i:s') . "

İyi günler dileriz.

Meka Meyvecilik
$to
+90 546 802 19 91
";

$auto_reply_headers = [
    'From: Meka Meyvecilik <' . $to . '>',
    'Content-Type: text/plain; charset=UTF-8',
    'X-Mailer: PHP/' . phpversion()
];

// Otomatik yanıt gönder
mail($email, $auto_reply_subject, $auto_reply_body, implode("\r\n", $auto_reply_headers));

// Sonuç sayfasına yönlendir
if ($mail_sent) {
    header('Location: ?page=contact&success=1');
} else {
    header('Location: ?page=contact&error=send_failed');
}
exit;
?>